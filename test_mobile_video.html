<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Video Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Mobile video specific styles */
        @media (max-width: 768px) {
            .video-container {
                height: 100vh;
                width: 100vw;
            }
            
            .video-container video {
                width: 100% !important;
                height: 100% !important;
                object-fit: contain;
                background: #000;
            }
            
            .video-container iframe {
                width: 100% !important;
                height: 100% !important;
            }
            
            /* Ensure video is properly sized */
            video::-webkit-media-controls-panel {
                background-color: rgba(0, 0, 0, 0.8);
            }
            
            video::-webkit-media-controls-play-button {
                background-color: rgba(255, 255, 255, 0.8);
                border-radius: 50%;
            }
        }

        .action-btn {
            transition: all 0.3s ease;
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto p-4">
        <h1 class="text-2xl font-bold mb-4 text-center">Mobile Video Player Test</h1>
        
        <!-- Desktop/Mobile Toggle -->
        <div class="mb-4 text-center">
            <button onclick="toggleView()" id="viewToggle" class="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded">
                Switch to Mobile View
            </button>
        </div>

        <!-- Video Container -->
        <div id="videoContainer" class="relative bg-black rounded-lg overflow-hidden">
            <!-- Desktop Layout -->
            <div id="desktopView" class="hidden md:flex justify-center items-center">
                <div class="video-container relative bg-black rounded-lg overflow-hidden shadow-2xl w-96 h-[680px]" data-video-id="1" data-video-type="upload">
                    <!-- HTML5 Video Player -->
                    <video
                        class="w-full h-full object-contain bg-black"
                        controls
                        preload="metadata"
                        playsinline
                        webkit-playsinline
                        id="video-desktop-1"
                        data-video-id="1"
                        onloadedmetadata="console.log('Desktop video loaded')"
                        onerror="console.error('Desktop video failed to load')"
                    >
                        <source src="http://localhost/react-news/uploads/sample_video.mp4" type="video/mp4">
                        <p class="text-white text-center p-4">Browser Anda tidak mendukung video HTML5.</p>
                    </video>
                </div>
            </div>

            <!-- Mobile Layout -->
            <div id="mobileView" class="md:hidden w-full mx-auto video-container relative bg-gray-900 h-screen" data-video-id="1" data-video-type="upload">
                <!-- HTML5 Video Player -->
                <video
                    class="w-full h-full object-contain bg-black"
                    controls
                    preload="metadata"
                    playsinline
                    webkit-playsinline
                    id="video-mobile-1"
                    data-video-id="1"
                    onloadedmetadata="console.log('Mobile video loaded')"
                    onerror="console.error('Mobile video failed to load')"
                    onclick="handleMobileVideoClick(this, 1)"
                >
                    <source src="http://localhost/react-news/uploads/sample_video.mp4" type="video/mp4">
                    <p class="text-white text-center p-4">Browser Anda tidak mendukung video HTML5.</p>
                </video>

                <!-- Mobile Right Side Actions (TikTok Style) -->
                <div class="absolute right-3 bottom-28 flex flex-col items-center space-y-3 z-50">
                    <!-- Like Button -->
                    <div class="flex flex-col items-center">
                        <button
                            onclick="testMobileAction('like'); event.stopPropagation();"
                            class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                            style="pointer-events: auto;"
                        >
                            <i class="fas fa-heart text-white text-lg pointer-events-none"></i>
                        </button>
                        <span class="text-white text-xs font-semibold">123</span>
                    </div>

                    <!-- Comment Button -->
                    <div class="flex flex-col items-center">
                        <button
                            onclick="testMobileAction('comment'); event.stopPropagation();"
                            class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                            style="pointer-events: auto;"
                        >
                            <i class="fas fa-comment-dots text-white text-lg pointer-events-none"></i>
                        </button>
                        <span class="text-white text-xs font-semibold">45</span>
                    </div>

                    <!-- Save Button -->
                    <div class="flex flex-col items-center">
                        <button
                            onclick="testMobileAction('save'); event.stopPropagation();"
                            class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-1 transition-all duration-300 cursor-pointer z-50 relative"
                            style="pointer-events: auto;"
                        >
                            <i class="fas fa-bookmark text-white text-lg pointer-events-none"></i>
                        </button>
                        <span class="text-white text-xs font-semibold">Simpan</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="mt-4 bg-gray-800 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-2">Test Results:</h3>
            <div id="testResults" class="space-y-1 text-sm">
                <p class="text-gray-400">Test results will appear here...</p>
            </div>
        </div>

        <!-- Instructions -->
        <div class="mt-4 bg-blue-900 p-4 rounded-lg">
            <h3 class="text-lg font-semibold mb-2">Instructions:</h3>
            <ul class="text-sm space-y-1">
                <li>• On mobile: Video should fill the screen and play when tapped</li>
                <li>• Action buttons (like, comment, save) should be clickable</li>
                <li>• Video controls should be visible and functional</li>
                <li>• Video should maintain aspect ratio (object-fit: contain)</li>
            </ul>
        </div>
    </div>

    <script>
        let isMobileView = window.innerWidth < 768;

        function toggleView() {
            const desktopView = document.getElementById('desktopView');
            const mobileView = document.getElementById('mobileView');
            const toggleBtn = document.getElementById('viewToggle');

            if (isMobileView) {
                // Switch to desktop view
                desktopView.classList.remove('hidden');
                desktopView.classList.add('flex');
                mobileView.classList.add('hidden');
                toggleBtn.textContent = 'Switch to Mobile View';
                isMobileView = false;
                addTestResult('Switched to Desktop View', 'text-blue-400');
            } else {
                // Switch to mobile view
                desktopView.classList.add('hidden');
                desktopView.classList.remove('flex');
                mobileView.classList.remove('hidden');
                toggleBtn.textContent = 'Switch to Desktop View';
                isMobileView = true;
                addTestResult('Switched to Mobile View', 'text-blue-400');
            }
        }

        function handleMobileVideoClick(videoElement, videoId) {
            console.log('📱 Mobile video clicked:', videoId);
            addTestResult('Mobile video clicked - attempting to play/pause', 'text-green-400');
            
            // Force video to play/pause
            if (videoElement.paused) {
                videoElement.play().then(() => {
                    console.log('✅ Mobile video started playing');
                    addTestResult('✅ Mobile video started playing', 'text-green-400');
                }).catch(error => {
                    console.error('❌ Error playing mobile video:', error);
                    addTestResult('❌ Error playing mobile video: ' + error.message, 'text-red-400');
                });
            } else {
                videoElement.pause();
                console.log('⏸️ Mobile video paused');
                addTestResult('⏸️ Mobile video paused', 'text-yellow-400');
            }
        }

        function testMobileAction(action) {
            console.log(`📱 Mobile ${action} button clicked`);
            addTestResult(`📱 Mobile ${action} button clicked`, 'text-green-400');
        }

        function addTestResult(message, colorClass) {
            const resultsDiv = document.getElementById('testResults');
            const p = document.createElement('p');
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            p.className = colorClass;
            resultsDiv.appendChild(p);
            
            // Keep only last 10 results
            while (resultsDiv.children.length > 10) {
                resultsDiv.removeChild(resultsDiv.firstChild);
            }
        }

        // Initialize view based on screen size
        document.addEventListener('DOMContentLoaded', function() {
            if (isMobileView) {
                toggleView();
            }
            addTestResult('Test page loaded', 'text-blue-400');
        });

        // Handle video events
        document.querySelectorAll('video').forEach(video => {
            video.addEventListener('play', function() {
                addTestResult(`Video started playing (${this.id})`, 'text-green-400');
            });
            
            video.addEventListener('pause', function() {
                addTestResult(`Video paused (${this.id})`, 'text-yellow-400');
            });
            
            video.addEventListener('loadedmetadata', function() {
                addTestResult(`Video metadata loaded (${this.id})`, 'text-blue-400');
            });
            
            video.addEventListener('error', function() {
                addTestResult(`Video error (${this.id})`, 'text-red-400');
            });
        });
    </script>
</body>
</html>
