<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    echo "Updating videos table...\n";
    
    // Read and execute SQL file
    $sql = file_get_contents('update_videos_table.sql');
    $statements = explode(';', $sql);
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            try {
                $pdo->exec($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (Exception $e) {
                echo "✗ Error: " . $e->getMessage() . "\n";
                echo "Statement: " . $statement . "\n";
            }
        }
    }
    
    echo "\nDatabase update completed!\n";
    
    // Show current table structure
    echo "\nCurrent videos table structure:\n";
    $stmt = $pdo->query("DESCRIBE videos");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($columns as $column) {
        echo "- {$column['Field']}: {$column['Type']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
