<?php
// Create sample video files for testing

$uploadsDir = __DIR__ . '/uploads/';

// Create uploads directory if it doesn't exist
if (!is_dir($uploadsDir)) {
    if (mkdir($uploadsDir, 0755, true)) {
        echo "✓ Created uploads directory: $uploadsDir\n";
    } else {
        echo "✗ Failed to create uploads directory\n";
        exit(1);
    }
} else {
    echo "✓ Uploads directory already exists\n";
}

// Create a simple HTML5 video test file (placeholder)
$sampleVideoContent = <<<HTML
<!DOCTYPE html>
<html>
<head>
    <title>Sample Video Placeholder</title>
</head>
<body style="margin:0; background:#000; color:#fff; display:flex; align-items:center; justify-content:center; height:100vh; font-family:Arial;">
    <div style="text-align:center;">
        <h1>Sample Video File</h1>
        <p>This is a placeholder for video testing</p>
        <p>In production, this would be an actual video file</p>
    </div>
</body>
</html>
HTML;

// Create sample files for different formats
$sampleFiles = [
    'sample_video.mp4' => 'video/mp4',
    'sample_video.avi' => 'video/avi', 
    'sample_video.mov' => 'video/quicktime',
    'sample_video.webm' => 'video/webm',
    'video_1752639344_4800.mp4' => 'video/mp4' // The specific file from error
];

foreach ($sampleFiles as $filename => $mimeType) {
    $filePath = $uploadsDir . $filename;
    
    // Create a simple text file as placeholder (in real scenario, these would be actual video files)
    $content = "Sample video file: $filename\nMIME Type: $mimeType\nCreated: " . date('Y-m-d H:i:s');
    
    if (file_put_contents($filePath, $content)) {
        echo "✓ Created sample file: $filename\n";
    } else {
        echo "✗ Failed to create: $filename\n";
    }
}

// Create .htaccess file to allow video file access
$htaccessContent = <<<HTACCESS
# Allow video file access
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    Header set Content-Type video/mp4
    Header set Accept-Ranges bytes
</FilesMatch>

# Enable CORS for video files
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</FilesMatch>

# Cache video files
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>
HTACCESS;

$htaccessPath = $uploadsDir . '.htaccess';
if (file_put_contents($htaccessPath, $htaccessContent)) {
    echo "✓ Created .htaccess file for video access\n";
} else {
    echo "✗ Failed to create .htaccess file\n";
}

// Test file access
echo "\nTesting file access:\n";
foreach ($sampleFiles as $filename => $mimeType) {
    $webPath = "http://localhost/react-news/uploads/$filename";
    echo "- $filename: $webPath\n";
}

echo "\nSample files created successfully!\n";
echo "You can now test video playback in the application.\n";

// Update database with correct paths
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    echo "\nUpdating database video paths...\n";
    
    // Update existing upload videos to have correct paths
    $stmt = $pdo->prepare("UPDATE videos SET video_path = CONCAT('http://localhost/react-news', video_path) WHERE video_type = 'upload' AND video_path NOT LIKE 'http%'");
    $result = $stmt->execute();
    
    if ($result) {
        $affected = $stmt->rowCount();
        echo "✓ Updated $affected video paths in database\n";
    }
    
    // Show current video records
    $stmt = $pdo->query("SELECT id, title, video_type, video_path, file_format FROM videos WHERE video_type = 'upload' ORDER BY created_at DESC LIMIT 5");
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nCurrent upload videos in database:\n";
    foreach ($videos as $video) {
        echo "- ID: {$video['id']}, Title: {$video['title']}\n";
        echo "  Path: {$video['video_path']}\n";
        echo "  Format: {$video['file_format']}\n\n";
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage() . "\n";
}
?>
