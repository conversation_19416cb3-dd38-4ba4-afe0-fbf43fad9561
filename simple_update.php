<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    echo "Adding columns to videos table...\n";
    
    // Add columns one by one
    $columns = [
        "ALTER TABLE `videos` ADD COLUMN `video_path` VARCHAR(500) NULL AFTER `youtube_id`",
        "ALTER TABLE `videos` ADD COLUMN `file_size` BIGINT NULL COMMENT 'File size in bytes' AFTER `video_path`",
        "ALTER TABLE `videos` ADD COLUMN `file_format` VARCHAR(10) NULL COMMENT 'Video format' AFTER `file_size`",
        "ALTER TABLE `videos` ADD COLUMN `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube' AFTER `file_format`"
    ];
    
    foreach ($columns as $sql) {
        try {
            $pdo->exec($sql);
            echo "✓ " . substr($sql, 0, 60) . "...\n";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                echo "- Column already exists: " . substr($sql, 0, 60) . "...\n";
            } else {
                echo "✗ Error: " . $e->getMessage() . "\n";
            }
        }
    }
    
    // Update existing records
    try {
        $pdo->exec("UPDATE `videos` SET `video_type` = 'youtube' WHERE `youtube_url` IS NOT NULL AND `youtube_url` != ''");
        echo "✓ Updated existing records\n";
    } catch (Exception $e) {
        echo "✗ Error updating records: " . $e->getMessage() . "\n";
    }
    
    echo "\nDatabase update completed!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
