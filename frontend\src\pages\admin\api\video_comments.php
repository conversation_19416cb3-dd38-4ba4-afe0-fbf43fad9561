<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config.php';

// Get database connection
$pdo = getConnection();

// Get client IP address
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            // Get comments for a video
            $videoId = $_GET['video_id'] ?? null;
            if (!$videoId) {
                throw new Exception('Video ID is required');
            }

            $stmt = $pdo->prepare("
                SELECT 
                    id, video_id, name, comment, created_at,
                    (SELECT COUNT(*) FROM video_comments WHERE parent_id = vc.id) as replies_count
                FROM video_comments vc
                WHERE video_id = ? AND status = 'approved' AND parent_id IS NULL
                ORDER BY created_at DESC
                LIMIT 50
            ");
            $stmt->execute([$videoId]);
            $comments = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Format timestamps
            foreach ($comments as &$comment) {
                $comment['time_ago'] = timeAgo($comment['created_at']);
            }

            echo json_encode([
                'success' => true,
                'comments' => $comments
            ]);
            break;

        case 'POST':
            // Add new comment
            $videoId = $input['video_id'] ?? null;
            $name = trim($input['name'] ?? '');
            $email = trim($input['email'] ?? '');
            $comment = trim($input['comment'] ?? '');

            if (!$videoId || !$name || !$comment) {
                throw new Exception('Video ID, name, and comment are required');
            }

            if (strlen($comment) < 3) {
                throw new Exception('Comment must be at least 3 characters long');
            }

            if (strlen($comment) > 1000) {
                throw new Exception('Comment is too long (max 1000 characters)');
            }

            $ip = getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if user has commented recently (spam protection)
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM video_comments 
                WHERE ip_address = ? AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
            ");
            $stmt->execute([$ip]);
            $recentComments = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($recentComments['count'] > 0) {
                throw new Exception('Please wait a moment before commenting again');
            }

            // Insert comment
            $stmt = $pdo->prepare("
                INSERT INTO video_comments (video_id, ip_address, user_agent, name, email, comment, status, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'approved', NOW())
            ");
            $stmt->execute([$videoId, $ip, $userAgent, $name, $email, $comment]);

            $commentId = $pdo->lastInsertId();

            // Update video comments count
            $stmt = $pdo->prepare("
                UPDATE videos 
                SET comments_count = (
                    SELECT COUNT(*) FROM video_comments 
                    WHERE video_id = ? AND status = 'approved'
                ) 
                WHERE id = ?
            ");
            $stmt->execute([$videoId, $videoId]);

            // Get the new comment
            $stmt = $pdo->prepare("
                SELECT id, video_id, name, comment, created_at
                FROM video_comments 
                WHERE id = ?
            ");
            $stmt->execute([$commentId]);
            $newComment = $stmt->fetch(PDO::FETCH_ASSOC);
            $newComment['time_ago'] = 'Baru saja';

            echo json_encode([
                'success' => true,
                'message' => 'Comment added successfully',
                'comment' => $newComment
            ]);
            break;

        default:
            throw new Exception('Method not allowed');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function timeAgo($datetime) {
    $time = time() - strtotime($datetime);
    
    if ($time < 60) return 'Baru saja';
    if ($time < 3600) return floor($time/60) . ' menit lalu';
    if ($time < 86400) return floor($time/3600) . ' jam lalu';
    if ($time < 2592000) return floor($time/86400) . ' hari lalu';
    if ($time < 31536000) return floor($time/2592000) . ' bulan lalu';
    return floor($time/31536000) . ' tahun lalu';
}
?>
