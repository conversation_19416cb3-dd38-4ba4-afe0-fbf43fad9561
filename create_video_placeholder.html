<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Placeholder Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px 0;
            max-width: 100%;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .video-preview {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Video Placeholder Generator</h1>
        <div class="info">
            <p><strong>Info:</strong> This tool creates placeholder videos for testing the video player functionality. 
            In a real application, users would upload actual video files.</p>
        </div>

        <h2>Generate Video Placeholder</h2>
        <canvas id="videoCanvas" width="640" height="360"></canvas>
        <br>
        <button onclick="generatePlaceholder()">Generate Placeholder</button>
        <button onclick="downloadVideo()">Download as WebM</button>
        
        <div class="video-preview">
            <h3>Preview:</h3>
            <video id="previewVideo" controls width="640" height="360" style="display:none;">
                Your browser does not support the video tag.
            </video>
        </div>

        <h2>Test Different Formats</h2>
        <div>
            <h3>MP4 Format Test</h3>
            <video controls width="320" height="180">
                <source src="/react-news/uploads/sample_video.mp4" type="video/mp4">
                <p>MP4 video not supported</p>
            </video>
        </div>

        <div>
            <h3>WebM Format Test</h3>
            <video controls width="320" height="180">
                <source src="/react-news/uploads/sample_video.webm" type="video/webm">
                <p>WebM video not supported</p>
            </video>
        </div>

        <div>
            <h3>Error Handling Test</h3>
            <video controls width="320" height="180" onerror="handleTestError(this)">
                <source src="/react-news/uploads/nonexistent_video.mp4" type="video/mp4">
                <p>This video should show an error</p>
            </video>
        </div>
    </div>

    <script>
        let mediaRecorder;
        let recordedChunks = [];

        function generatePlaceholder() {
            const canvas = document.getElementById('videoCanvas');
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.fillStyle = '#2c3e50';
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Draw placeholder content
            ctx.fillStyle = '#ecf0f1';
            ctx.font = 'bold 48px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Sample Video', canvas.width/2, canvas.height/2 - 50);
            
            ctx.font = '24px Arial';
            ctx.fillText('Placeholder for Testing', canvas.width/2, canvas.height/2);
            
            ctx.font = '18px Arial';
            ctx.fillText('640x360 • ' + new Date().toLocaleString(), canvas.width/2, canvas.height/2 + 40);
            
            // Add some animated elements
            const time = Date.now() / 1000;
            ctx.fillStyle = '#3498db';
            ctx.beginPath();
            ctx.arc(100 + Math.sin(time) * 20, 100, 20, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = '#e74c3c';
            ctx.beginPath();
            ctx.arc(canvas.width - 100 + Math.cos(time) * 20, canvas.height - 100, 20, 0, Math.PI * 2);
            ctx.fill();
            
            console.log('Placeholder generated');
        }

        function downloadVideo() {
            const canvas = document.getElementById('videoCanvas');
            const stream = canvas.captureStream(30); // 30 FPS
            
            recordedChunks = [];
            mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'video/webm;codecs=vp9'
            });
            
            mediaRecorder.ondataavailable = function(event) {
                if (event.data.size > 0) {
                    recordedChunks.push(event.data);
                }
            };
            
            mediaRecorder.onstop = function() {
                const blob = new Blob(recordedChunks, {
                    type: 'video/webm'
                });
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'sample_video_placeholder.webm';
                a.click();
                
                // Show preview
                const previewVideo = document.getElementById('previewVideo');
                previewVideo.src = url;
                previewVideo.style.display = 'block';
                
                URL.revokeObjectURL(url);
            };
            
            // Record for 3 seconds
            mediaRecorder.start();
            
            // Animate during recording
            let frameCount = 0;
            const animate = () => {
                generatePlaceholder();
                frameCount++;
                if (frameCount < 90) { // 3 seconds at 30fps
                    requestAnimationFrame(animate);
                } else {
                    mediaRecorder.stop();
                }
            };
            
            animate();
        }

        function handleTestError(videoElement) {
            console.error('Test video failed to load');
            videoElement.style.border = '2px solid red';
            videoElement.insertAdjacentHTML('afterend', 
                '<p style="color: red; font-size: 12px;">❌ Video failed to load (expected for testing)</p>'
            );
        }

        // Initialize
        generatePlaceholder();
        
        // Auto-refresh placeholder every 2 seconds for animation
        setInterval(generatePlaceholder, 2000);
    </script>
</body>
</html>
