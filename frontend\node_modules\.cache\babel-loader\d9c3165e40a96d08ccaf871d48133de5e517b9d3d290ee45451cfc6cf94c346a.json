{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16',\n      // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        cursor: 'pointer',\n        objectFit: 'cover' // Maintain aspect ratio\n      },\n      onClick: handleVideoClick,\n      allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n      allowFullScreen: true,\n      title: video.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 222,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 278,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 284,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 327,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"YfQkD9vK244jX6up3O1KMuen6o4=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data.filter(video => video.status === 'published') // Only published videos\n          .map(video => {\n            var _video$content, _video$category;\n            const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n            return {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: youtubeId,\n              title: video.title || 'Untitled Video',\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...' || 'No description available',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: video.created_by || 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0,\n                views: parseInt(video.views) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video',\n              thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n            };\n          });\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n    const handleScroll = e => {\n      if (isScrolling) return;\n      isScrolling = true;\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n      setTimeout(() => {\n        isScrolling = false;\n      }, 300);\n    };\n    const handleTouchStart = e => {\n      startY = e.touches[0].clientY;\n    };\n    const handleTouchEnd = e => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n      if (Math.abs(diffY) > 50) {\n        // Minimum swipe distance\n        isScrolling = true;\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n        setTimeout(() => {\n          isScrolling = false;\n        }, 300);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n        sx: {\n          fontSize: 40,\n          opacity: 0.7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 1\n      },\n      children: \"Belum Ada Video\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        opacity: 0.7,\n        maxWidth: 300,\n        mb: 4\n      },\n      children: \"Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: reloadVideos,\n      sx: {\n        color: 'white',\n        bgcolor: 'rgba(255, 255, 255, 0.1)',\n        '&:hover': {\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        },\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-refresh\",\n        style: {\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 576,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        opacity: 0.5\n      },\n      children: \"Ketuk untuk memuat ulang\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 590,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 616,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 607,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 625,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 624,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 534,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 654,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 668,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 645,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 685,\n      columnNumber: 9\n    }, this) : videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n          transition: 'transform 0.3s ease-in-out'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoItem, {\n          video: video,\n          isActive: index === currentVideoIndex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 15\n        }, this)\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 712,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 728,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 675,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "toString", "handleVideoClick", "console", "log", "title", "fetch", "id", "method", "expressError", "error", "handleLike", "wasLiked", "prev", "response", "data", "json", "success", "liked", "handleSave", "handleShare", "shares", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "aspectRatio", "borderRadius", "children", "ref", "src", "youtubeId", "style", "border", "cursor", "objectFit", "onClick", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "right", "bottom", "zIndex", "flexDirection", "gap", "author", "avatar", "verified", "mt", "color", "fontSize", "fontWeight", "toFixed", "comments", "mb", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "description", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "loadVideos", "ok", "Error", "status", "Array", "isArray", "dbVideos", "filter", "_video$content", "_video$category", "youtube_id", "extractYouTubeId", "youtube_url", "videoUrl", "content", "substring", "split", "trim", "category", "toLowerCase", "name", "created_by", "parseInt", "comments_count", "views", "created_at", "toISOString", "thumbnail", "length", "url", "regExp", "match", "startY", "isScrolling", "handleScroll", "e", "deltaY", "setTimeout", "handleTouchStart", "touches", "clientY", "handleTouchEnd", "endY", "changedTouches", "diffY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "padding", "textAlign", "opacity", "variant", "className", "disabled", "LoadingVideoState", "borderTop", "animation", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16', // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    }}>\n      {/* YouTube Video Iframe */}\n      <iframe\n        ref={iframeRef}\n        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer',\n          objectFit: 'cover' // Maintain aspect ratio\n        }}\n        onClick={handleVideoClick}\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        allowFullScreen\n        title={video.title}\n      />\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('API Response:', data);\n\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data\n            .filter(video => video.status === 'published') // Only published videos\n            .map(video => {\n              const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n              return {\n                id: video.id,\n                videoUrl: video.youtube_url,\n                youtubeId: youtubeId,\n                title: video.title || 'Untitled Video',\n                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',\n                tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],\n                author: {\n                  name: video.created_by || 'News Reporter',\n                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                  verified: true\n                },\n                stats: {\n                  likes: parseInt(video.likes) || 0,\n                  comments: parseInt(video.comments_count) || 0,\n                  shares: parseInt(video.shares) || 0,\n                  views: parseInt(video.views) || 0\n                },\n                uploadDate: video.created_at || new Date().toISOString(),\n                duration: video.duration || '03:00',\n                category: video.category || 'Video',\n                thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n              };\n            });\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n\n    const handleScroll = (e) => {\n      if (isScrolling) return;\n      isScrolling = true;\n\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n\n      setTimeout(() => { isScrolling = false; }, 300);\n    };\n\n    const handleTouchStart = (e) => {\n      startY = e.touches[0].clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n\n      if (Math.abs(diffY) > 50) { // Minimum swipe distance\n        isScrolling = true;\n\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n\n        setTimeout(() => { isScrolling = false; }, 300);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    }}>\n      <Box sx={{\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      }}>\n        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />\n      </Box>\n\n      <Typography variant=\"h6\" sx={{ mb: 1 }}>\n        Belum Ada Video\n      </Typography>\n\n      <Typography variant=\"body2\" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>\n        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\n      </Typography>\n\n      {/* Refresh Button */}\n      <IconButton\n        onClick={reloadVideos}\n        sx={{\n          color: 'white',\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },\n          mb: 3\n        }}\n      >\n        <i className=\"fas fa-refresh\" style={{ fontSize: 20 }} />\n      </IconButton>\n\n      <Typography variant=\"caption\" sx={{ opacity: 0.5 }}>\n        Ketuk untuk memuat ulang\n      </Typography>\n\n      {/* Placeholder UI Elements */}\n      <Box sx={{\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      }}>\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <FavoriteBorderIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos.length > 0 ? (\n        <>\n          {videos.map((video, index) => (\n            <Box\n              key={video.id}\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                transition: 'transform 0.3s ease-in-out'\n              }}\n            >\n              <VideoItem\n                video={video}\n                isActive={index === currentVideoIndex}\n              />\n            </Box>\n          ))}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMC,WAAW,GAAGH,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMI,UAAU,GAAGJ,QAAQ,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMK,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCF,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BG,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEP,OAAO;MACjBQ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MACPC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,iCAAiCb,OAAO,IAAIE,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEpC,KAAK,CAACqC,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,IAAI;QACF,MAAMC,KAAK,CAAC,oCAAoCtC,KAAK,CAACuC,EAAE,OAAO,EAAE;UAC/DC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB;QACA,MAAMH,KAAK,CAAC,gGAAgGtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UACtHC,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGxC,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAACoC,IAAI,IAAIzC,OAAO,GAAGyC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACF;MACA,IAAIC,QAAQ;MACZ,IAAIC,IAAI;MAER,IAAI;QACFD,QAAQ,GAAG,MAAMR,KAAK,CAAC,oCAAoCtC,KAAK,CAACuC,EAAE,OAAO,EAAE;UAC1EC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOP,YAAY,EAAE;QACrB;QACAK,QAAQ,GAAG,MAAMR,KAAK,CAAC,4FAA4FtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UAC7HC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACA5C,UAAU,CAAC0C,IAAI,CAACG,KAAK,CAAC;QACtBzC,QAAQ,CAACsC,IAAI,CAACvC,KAAK,CAAC;MACtB,CAAC,MAAM;QACL;QACAH,UAAU,CAACuC,QAAQ,CAAC;QACpBnC,QAAQ,CAACoC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOH,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACArC,UAAU,CAACuC,QAAQ,CAAC;MACpBnC,QAAQ,CAACoC,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMM,UAAU,GAAGA,CAAA,KAAM;IACvB5C,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAM8C,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BjB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEpC,KAAK,CAACuC,EAAE,CAAC;IAErC,IAAI;MACF;MACA,IAAIO,QAAQ;MACZ,IAAIC,IAAI;MAER,IAAI;QACFD,QAAQ,GAAG,MAAMR,KAAK,CAAC,oCAAoCtC,KAAK,CAACuC,EAAE,QAAQ,EAAE;UAC3EC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAOP,YAAY,EAAE;QACrB;QACAK,QAAQ,GAAG,MAAMR,KAAK,CAAC,gGAAgGtC,KAAK,CAACuC,EAAE,EAAE,EAAE;UACjIC,MAAM,EAAE;QACV,CAAC,CAAC;QACFO,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACAjD,KAAK,CAACU,KAAK,CAAC2C,MAAM,GAAGN,IAAI,CAACM,MAAM;MAClC;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMY,aAAa,GAAGA,CAAA,KAAM;IAC1BnB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEpC,KAAK,CAACuC,EAAE,CAAC;EACnD,CAAC;EAED,MAAMgB,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACEnE,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAEnD,WAAW;MACnBoD,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAEpD,UAAU;MACpBqD,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,MAAM;MAAE;MACrBC,YAAY,EAAEhE,QAAQ,GAAG,CAAC,GAAG;IAC/B,CAAE;IAAAiE,QAAA,gBAEAlF,OAAA;MACEmF,GAAG,EAAEpE,SAAU;MACfqE,GAAG,EAAE9D,kBAAkB,CAAClB,KAAK,CAACiF,SAAS,EAAEhF,QAAQ,CAAE;MACnDiF,KAAK,EAAE;QACLd,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdgB,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,OAAO,CAAC;MACrB,CAAE;MACFC,OAAO,EAAEpD,gBAAiB;MAC1BqD,KAAK,EAAC,0FAA0F;MAChGC,eAAe;MACfnD,KAAK,EAAErC,KAAK,CAACqC;IAAM;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFhG,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2B,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTb,MAAM,EAAE;MACV,CAAE;MACFE,OAAO,EAAEpD;IAAiB;MAAAuD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFhG,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB6B,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXxB,OAAO,EAAE,MAAM;QACf0B,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNF,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAlF,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACb,MAAM;UACLiG,GAAG,EAAEhF,KAAK,CAACoG,MAAM,CAACC,MAAO;UACzBpC,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVgB,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD5F,KAAK,CAACoG,MAAM,CAACE,QAAQ,iBACpB1G,OAAA,CAAChB,GAAG;UAACqF,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClBM,YAAY,EAAE,KAAK;YACnBT,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB6B,EAAE,EAAE,CAAC,CAAC;YACNpB,MAAM,EAAE;UACV,CAAE;UAAAL,QAAA,eACAlF,OAAA,CAACf,UAAU;YAACoF,EAAE,EAAE;cAAEuC,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA5B,QAAA,EAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACTwG,OAAO,EAAE3C,UAAW;UACpBsB,EAAE,EAAE;YACFuC,KAAK,EAAEpG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCmE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAED1E,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACR,kBAAkB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvDtE,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEmG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGnG;QAAK;UAAAiF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACTwG,OAAO,EAAEhC,aAAc;UACvBW,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFlF,OAAA,CAACP,qBAAqB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD9E,KAAK,CAACU,KAAK,CAACkG;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACTwG,OAAO,EAAEnC,UAAW;UACpBc,EAAE,EAAE;YACFuC,KAAK,EAAElG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCiE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAEDxE,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhG,OAAA,CAACN,kBAAkB;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAE3D;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACTwG,OAAO,EAAElC,WAAY;UACrBa,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFlF,OAAA,CAACJ,SAAS;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD9E,KAAK,CAACU,KAAK,CAAC2C;QAAM;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhG,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB8B,MAAM,EAAE,EAAE;QACVF,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTE,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAlF,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE;QACN,CAAE;QAAA/B,QAAA,GACCvB,UAAU,CAACvD,KAAK,CAAC8G,UAAU,CAAC,EAAC,UAAG,EAAC9G,KAAK,CAAC+G,QAAQ;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbhG,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACduC,KAAK,EAAE,OAAO;UACdE,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE;QACd,CAAE;QAAAlC,QAAA,EACC9E,KAAK,CAACqC;MAAK;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbhG,OAAA,CAACf,UAAU;QAACoF,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE,GAAG;UACfxC,OAAO,EAAE,aAAa;UACtByC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BvC,QAAQ,EAAE;QACZ,CAAE;QAAAG,QAAA,EACC9E,KAAK,CAACmH;MAAW;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE2B,GAAG,EAAE,CAAC;UAAEiB,QAAQ,EAAE;QAAO,CAAE;QAAAtC,QAAA,EACpD9E,KAAK,CAACqH,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB5H,OAAA,CAACZ,IAAI;UAEHyI,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZzD,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnCiC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZtC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGiD,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAzF,EAAA,CAvWSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAAyI,EAAA,GANvB5H,SAAS;AAwWlB,eAAe,SAAS6H,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtJ,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACuJ,MAAM,EAAEC,SAAS,CAAC,GAAGxJ,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACyJ,OAAO,EAAEC,UAAU,CAAC,GAAG1J,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMyJ,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACFhG,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;;QAE9C;QACA,MAAMU,QAAQ,GAAG,MAAMR,KAAK,CAAC,gFAAgF,CAAC;QAE9G,IAAI,CAACQ,QAAQ,CAACuF,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBxF,QAAQ,CAACyF,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMxF,IAAI,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAClCb,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEW,IAAI,CAAC;QAElC,IAAIA,IAAI,CAACE,OAAO,IAAIuF,KAAK,CAACC,OAAO,CAAC1F,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAM2F,QAAQ,GAAG3F,IAAI,CAACA,IAAI,CACvB4F,MAAM,CAAC3I,KAAK,IAAIA,KAAK,CAACuI,MAAM,KAAK,WAAW,CAAC,CAAC;UAAA,CAC9CjB,GAAG,CAACtH,KAAK,IAAI;YAAA,IAAA4I,cAAA,EAAAC,eAAA;YACZ,MAAM5D,SAAS,GAAGjF,KAAK,CAAC8I,UAAU,IAAIC,gBAAgB,CAAC/I,KAAK,CAACgJ,WAAW,CAAC;YACzE,OAAO;cACLzG,EAAE,EAAEvC,KAAK,CAACuC,EAAE;cACZ0G,QAAQ,EAAEjJ,KAAK,CAACgJ,WAAW;cAC3B/D,SAAS,EAAEA,SAAS;cACpB5C,KAAK,EAAErC,KAAK,CAACqC,KAAK,IAAI,gBAAgB;cACtC8E,WAAW,EAAEnH,KAAK,CAACmH,WAAW,IAAI,EAAAyB,cAAA,GAAA5I,KAAK,CAACkJ,OAAO,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK,IAAI,0BAA0B;cACxG9B,IAAI,EAAErH,KAAK,CAACqH,IAAI,GAAGrH,KAAK,CAACqH,IAAI,CAAC+B,KAAK,CAAC,GAAG,CAAC,CAAC9B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC8B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAAR,eAAA,GAAA7I,KAAK,CAACsJ,QAAQ,cAAAT,eAAA,uBAAdA,eAAA,CAAgBU,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cAC5GnD,MAAM,EAAE;gBACNoD,IAAI,EAAExJ,KAAK,CAACyJ,UAAU,IAAI,eAAe;gBACzCpD,MAAM,EAAE,gCAAgC,IAAIrG,KAAK,CAACuC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9D+D,QAAQ,EAAE;cACZ,CAAC;cACD5F,KAAK,EAAE;gBACLF,KAAK,EAAEkJ,QAAQ,CAAC1J,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCoG,QAAQ,EAAE8C,QAAQ,CAAC1J,KAAK,CAAC2J,cAAc,CAAC,IAAI,CAAC;gBAC7CtG,MAAM,EAAEqG,QAAQ,CAAC1J,KAAK,CAACqD,MAAM,CAAC,IAAI,CAAC;gBACnCuG,KAAK,EAAEF,QAAQ,CAAC1J,KAAK,CAAC4J,KAAK,CAAC,IAAI;cAClC,CAAC;cACD9C,UAAU,EAAE9G,KAAK,CAAC6J,UAAU,IAAI,IAAInG,IAAI,CAAC,CAAC,CAACoG,WAAW,CAAC,CAAC;cACxD/C,QAAQ,EAAE/G,KAAK,CAAC+G,QAAQ,IAAI,OAAO;cACnCuC,QAAQ,EAAEtJ,KAAK,CAACsJ,QAAQ,IAAI,OAAO;cACnCS,SAAS,EAAE,8BAA8B9E,SAAS;YACpD,CAAC;UACH,CAAC,CAAC;UAEJ9C,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsG,QAAQ,CAAC;;UAE1C;UACA,IAAIA,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;YACvB/B,SAAS,CAACS,QAAQ,CAAC;YACnBvG,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEsG,QAAQ,CAACsB,MAAM,CAAC;UAChE,CAAC,MAAM;YACL7H,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;YAChE6F,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACL9F,OAAO,CAACO,KAAK,CAAC,8BAA8B,EAAEK,IAAI,CAAC;UACnDkF,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAOvF,KAAK,EAAE;QACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAuF,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMW,gBAAgB,GAAIkB,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMC,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACH,MAAM,KAAK,EAAE,GAAIG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACAxL,SAAS,CAAC,MAAM;IACd,IAAIqJ,MAAM,CAACgC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,IAAII,MAAM,GAAG,CAAC;IACd,IAAIC,WAAW,GAAG,KAAK;IAEvB,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIF,WAAW,EAAE;MACjBA,WAAW,GAAG,IAAI;MAElB,IAAIE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI1C,iBAAiB,GAAGE,MAAM,CAACgC,MAAM,GAAG,CAAC,EAAE;QACzDjC,oBAAoB,CAAClF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAI0H,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI1C,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAAClF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;MAEA4H,UAAU,CAAC,MAAM;QAAEJ,WAAW,GAAG,KAAK;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC;IAED,MAAMK,gBAAgB,GAAIH,CAAC,IAAK;MAC9BH,MAAM,GAAGG,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC/B,CAAC;IAED,MAAMC,cAAc,GAAIN,CAAC,IAAK;MAC5B,IAAIF,WAAW,EAAE;MACjB,MAAMS,IAAI,GAAGP,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;MACxC,MAAMI,KAAK,GAAGZ,MAAM,GAAGU,IAAI;MAE3B,IAAIjH,IAAI,CAACC,GAAG,CAACkH,KAAK,CAAC,GAAG,EAAE,EAAE;QAAE;QAC1BX,WAAW,GAAG,IAAI;QAElB,IAAIW,KAAK,GAAG,CAAC,IAAIlD,iBAAiB,GAAGE,MAAM,CAACgC,MAAM,GAAG,CAAC,EAAE;UACtD;UACAjC,oBAAoB,CAAClF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAImI,KAAK,GAAG,CAAC,IAAIlD,iBAAiB,GAAG,CAAC,EAAE;UAC7C;UACAC,oBAAoB,CAAClF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC;QAEA4H,UAAU,CAAC,MAAM;UAAEJ,WAAW,GAAG,KAAK;QAAE,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC;IAEDY,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEZ,YAAY,CAAC;IAC9CW,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,CAAC;IACvDO,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEL,cAAc,CAAC;IAEnD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEb,YAAY,CAAC;MACjDW,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;MAC1DO,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC/C,iBAAiB,EAAEE,MAAM,CAACgC,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzBjD,UAAU,CAAC,IAAI,CAAC;IAChB;IACA8C,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB3L,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE,OAAO;MACdgF,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAE;IAAA3G,QAAA,gBACAlF,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBN,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBmC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eACAlF,OAAA,CAACH,aAAa;QAACwE,EAAE,EAAE;UAAEwC,QAAQ,EAAE,EAAE;UAAEiF,OAAO,EAAE;QAAI;MAAE;QAAAjG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENhG,OAAA,CAACf,UAAU;MAAC8M,OAAO,EAAC,IAAI;MAAC1H,EAAE,EAAE;QAAE4C,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbhG,OAAA,CAACf,UAAU;MAAC8M,OAAO,EAAC,OAAO;MAAC1H,EAAE,EAAE;QAAEyH,OAAO,EAAE,GAAG;QAAErH,QAAQ,EAAE,GAAG;QAAEwC,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExE;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhG,OAAA,CAACd,UAAU;MACTwG,OAAO,EAAE8F,YAAa;MACtBnH,EAAE,EAAE;QACFuC,KAAK,EAAE,OAAO;QACdjC,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE;UAAEA,OAAO,EAAE;QAA2B,CAAC;QAClDsC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eAEFlF,OAAA;QAAGgM,SAAS,EAAC,gBAAgB;QAAC1G,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEbhG,OAAA,CAACf,UAAU;MAAC8M,OAAO,EAAC,SAAS;MAAC1H,EAAE,EAAE;QAAEyH,OAAO,EAAE;MAAI,CAAE;MAAA5G,QAAA,EAAC;IAEpD;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhG,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPO,OAAO,EAAE,MAAM;QACf2B,GAAG,EAAE,CAAC;QACNI,EAAE,EAAE;MACN,CAAE;MAAAzB,QAAA,gBAEAlF,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACT+M,QAAQ;UACR5H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEmH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA5G,QAAA,eAEFlF,OAAA,CAACR,kBAAkB;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEmF,OAAO,EAAE;UAAI,CAAE;UAAA5G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACT+M,QAAQ;UACR5H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEmH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA5G,QAAA,eAEFlF,OAAA,CAACP,qBAAqB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEmF,OAAO,EAAE;UAAI,CAAE;UAAA5G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElF,OAAA,CAACd,UAAU;UACT+M,QAAQ;UACR5H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEmH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA5G,QAAA,eAEFlF,OAAA,CAACJ,SAAS;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbhG,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEmF,OAAO,EAAE;UAAI,CAAE;UAAA5G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMkG,iBAAiB,GAAGA,CAAA,kBACxBlM,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE;IACT,CAAE;IAAA1B,QAAA,gBACAlF,OAAA,CAAChB,GAAG;MAACqF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBM,MAAM,EAAE,iCAAiC;QACzC4G,SAAS,EAAE,iBAAiB;QAC5BC,SAAS,EAAE,yBAAyB;QACpCnF,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAEoF,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAAxG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAELhG,OAAA,CAACf,UAAU;MAAC8M,OAAO,EAAC,OAAO;MAAA7G,QAAA,EAAC;IAE5B;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAED,oBACEhG,OAAA,CAAChB,GAAG;IAACqF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE,UAAU;MACpBK,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAI,QAAA,EACCoD,OAAO,gBACNtI,OAAA,CAACkM,iBAAiB;MAAArG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBoC,MAAM,CAACgC,MAAM,GAAG,CAAC,gBACnBpK,OAAA,CAAAE,SAAA;MAAAgF,QAAA,GACGkD,MAAM,CAACV,GAAG,CAAC,CAACtH,KAAK,EAAEwH,KAAK,kBACvB5H,OAAA,CAAChB,GAAG;QAEFqF,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACP1B,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBuH,SAAS,EAAE,cAAc,CAACzE,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;UAC9DoE,UAAU,EAAE;QACd,CAAE;QAAApH,QAAA,eAEFlF,OAAA,CAACG,SAAS;UACRC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEuH,KAAK,KAAKM;QAAkB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC,GAjBG5F,KAAK,CAACuC,EAAE;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBV,CACN,CAAC,eAGFhG,OAAA,CAAChB,GAAG;QAACqF,EAAE,EAAE;UACPC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,EAAE;UACPE,KAAK,EAAE,EAAE;UACTxB,OAAO,EAAE,oBAAoB;UAC7BM,YAAY,EAAE,CAAC;UACfsH,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLnG,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,eACAlF,OAAA,CAACf,UAAU;UAACoF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA3B,QAAA,GAC9CgD,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAACgC,MAAM;QAAA;UAAAvE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEHhG,OAAA,CAAC2L,eAAe;MAAA9F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACiC,GAAA,CA7VuBD,SAAS;AAAAyE,GAAA,GAATzE,SAAS;AAAA,IAAAD,EAAA,EAAA0E,GAAA;AAAAC,YAAA,CAAA3E,EAAA;AAAA2E,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}