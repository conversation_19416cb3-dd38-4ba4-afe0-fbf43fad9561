<?php
require_once __DIR__ . '/../admin/config.php';

// Get database connection
$pdo = getConnection();

// Get published videos
try {
    $stmt = $pdo->prepare("
        SELECT 
            id, title, description, content, youtube_url, youtube_id,
            thumbnail, category, tags, duration, status, views, likes,
            shares, comments_count, featured, created_at, updated_at
        FROM videos 
        WHERE status = 'published' 
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $videos = [];
    error_log("Error fetching videos: " . $e->getMessage());
}

// Extract YouTube ID from URL
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : 'dQw4w9WgXcQ';
}

// Generate YouTube embed URL
function getYouTubeEmbedUrl($videoId, $autoplay = false) {
    $params = http_build_query([
        'autoplay' => $autoplay ? '1' : '0',
        'mute' => '1',
        'controls' => '1',
        'loop' => '1',
        'playlist' => $videoId,
        'rel' => '0',
        'showinfo' => '0',
        'modestbranding' => '1',
        'iv_load_policy' => '3',
        'fs' => '1',
        'disablekb' => '0'
    ]);
    return "https://www.youtube.com/embed/{$videoId}?{$params}";
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video - News App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .video-container {
            aspect-ratio: 9/16;
            max-width: 400px;
        }
        .video-scroll {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh;
        }
        .video-item {
            scroll-snap-align: start;
            height: 100vh;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        @media (max-width: 768px) {
            .video-container {
                max-width: 100%;
            }
        }
    </style>
</head>
<body class="bg-black text-white overflow-hidden">
    <div class="video-scroll" id="videoContainer">
        <?php if (empty($videos)): ?>
            <!-- Empty State -->
            <div class="video-item flex flex-col items-center justify-center">
                <i class="fas fa-video text-6xl text-gray-400 mb-4"></i>
                <h2 class="text-2xl font-bold mb-2">Belum Ada Video</h2>
                <p class="text-gray-400 text-center">Video akan muncul di sini setelah admin menambahkannya</p>
            </div>
        <?php else: ?>
            <?php foreach ($videos as $index => $video): ?>
                <?php 
                $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);
                $embedUrl = getYouTubeEmbedUrl($youtubeId, $index === 0);
                ?>
                <div class="video-item relative bg-black flex items-center justify-center" data-video-id="<?= $video['id'] ?>">
                    <!-- Debug Info -->
                    <div class="absolute top-4 left-4 bg-black bg-opacity-70 text-white text-xs p-2 rounded z-50">
                        ID: <?= htmlspecialchars($youtubeId) ?> | <?= htmlspecialchars($video['title']) ?>
                    </div>

                    <!-- Video Container -->
                    <div class="w-full max-w-sm mx-auto video-container relative bg-gray-900">
                        <!-- YouTube Iframe -->
                        <iframe 
                            src="<?= htmlspecialchars($embedUrl) ?>"
                            class="w-full h-full border-0"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                            title="<?= htmlspecialchars($video['title']) ?>"
                            onload="console.log('Video loaded: <?= $youtubeId ?>')"
                            onerror="console.error('Video failed to load: <?= $youtubeId ?>')"
                        ></iframe>

                        <!-- Video Overlay for Interaction -->
                        <div class="absolute inset-0 z-10 cursor-pointer" onclick="handleVideoClick(<?= $video['id'] ?>)"></div>
                    </div>

                    <!-- Right Side Actions (TikTok Style) -->
                    <div class="absolute right-4 bottom-24 flex flex-col items-center space-y-6 z-20">
                        <!-- Profile -->
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mb-2">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <i class="fas fa-plus text-red-500 text-xs"></i>
                        </div>

                        <!-- Like Button -->
                        <div class="flex flex-col items-center">
                            <button 
                                onclick="toggleLike(<?= $video['id'] ?>)" 
                                class="like-btn w-12 h-12 rounded-full bg-black bg-opacity-30 flex items-center justify-center mb-1 hover:bg-opacity-50 transition-all"
                                data-video-id="<?= $video['id'] ?>"
                            >
                                <i class="fas fa-heart text-white text-xl"></i>
                            </button>
                            <span class="text-white text-xs like-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['likes']) ?></span>
                        </div>

                        <!-- Comment Button -->
                        <div class="flex flex-col items-center">
                            <button class="w-12 h-12 rounded-full bg-black bg-opacity-30 flex items-center justify-center mb-1 hover:bg-opacity-50 transition-all">
                                <i class="fas fa-comment text-white text-xl"></i>
                            </button>
                            <span class="text-white text-xs"><?= number_format($video['comments_count']) ?></span>
                        </div>

                        <!-- Share Button -->
                        <div class="flex flex-col items-center">
                            <button 
                                onclick="shareVideo(<?= $video['id'] ?>)" 
                                class="w-12 h-12 rounded-full bg-black bg-opacity-30 flex items-center justify-center mb-1 hover:bg-opacity-50 transition-all"
                            >
                                <i class="fas fa-share text-white text-xl"></i>
                            </button>
                            <span class="text-white text-xs share-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['shares']) ?></span>
                        </div>

                        <!-- Save Button -->
                        <div class="flex flex-col items-center">
                            <button class="w-12 h-12 rounded-full bg-black bg-opacity-30 flex items-center justify-center hover:bg-opacity-50 transition-all">
                                <i class="fas fa-bookmark text-white text-xl"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Bottom Info -->
                    <div class="absolute bottom-4 left-4 right-20 z-20">
                        <!-- Title -->
                        <h3 class="text-white font-bold text-lg mb-2 line-clamp-2">
                            <?= htmlspecialchars($video['title']) ?>
                        </h3>

                        <!-- Description -->
                        <p class="text-white text-opacity-90 text-sm mb-3 line-clamp-2">
                            <?= htmlspecialchars($video['description'] ?: substr($video['content'], 0, 100) . '...') ?>
                        </p>

                        <!-- Tags -->
                        <div class="flex flex-wrap gap-2">
                            <?php 
                            $tags = $video['tags'] ? explode(',', $video['tags']) : [$video['category']];
                            foreach (array_slice($tags, 0, 3) as $tag): 
                                $tag = trim($tag);
                                if ($tag):
                            ?>
                                <span class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                                    #<?= htmlspecialchars($tag) ?>
                                </span>
                            <?php 
                                endif;
                            endforeach; 
                            ?>
                        </div>

                        <!-- Stats -->
                        <div class="flex items-center space-x-4 mt-2 text-xs text-white text-opacity-70">
                            <span><i class="fas fa-eye mr-1"></i><?= number_format($video['views']) ?></span>
                            <span><i class="fas fa-clock mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                        </div>
                    </div>

                    <!-- Video Counter -->
                    <div class="absolute top-4 right-4 bg-black bg-opacity-50 text-white text-xs px-3 py-1 rounded-full z-30">
                        <?= $index + 1 ?> / <?= count($videos) ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 z-40">
        <div class="flex justify-around items-center py-2">
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Home</span>
            </a>
            <a href="#" class="flex flex-col items-center py-2 px-4 text-blue-500">
                <i class="fas fa-play text-xl mb-1"></i>
                <span class="text-xs">Video</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-search text-xl mb-1"></i>
                <span class="text-xs">Cari</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-bookmark text-xl mb-1"></i>
                <span class="text-xs">Simpan</span>
            </a>
        </div>
    </div>

    <script>
        let currentVideoIndex = 0;
        const videos = <?= json_encode($videos) ?>;
        
        console.log('Loaded videos:', videos.length);

        // Handle video click to increment views
        async function handleVideoClick(videoId) {
            console.log('Video clicked:', videoId);
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ View count updated');
                }
            } catch (error) {
                console.error('❌ Failed to update view count:', error);
            }
        }

        // Toggle like functionality
        async function toggleLike(videoId) {
            console.log('Toggling like for video:', videoId);
            
            const likeBtn = document.querySelector(`.like-btn[data-video-id="${videoId}"] i`);
            const likeCount = document.querySelector(`.like-count[data-video-id="${videoId}"]`);
            
            // Optimistic update
            const isLiked = likeBtn.classList.contains('text-red-500');
            likeBtn.classList.toggle('text-red-500');
            likeBtn.classList.toggle('text-white');
            
            const currentCount = parseInt(likeCount.textContent.replace(/,/g, ''));
            likeCount.textContent = (isLiked ? currentCount - 1 : currentCount + 1).toLocaleString();
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Like toggled successfully');
                    // Update with server response if available
                    if (data.likes !== undefined) {
                        likeCount.textContent = parseInt(data.likes).toLocaleString();
                    }
                } else {
                    // Revert on failure
                    likeBtn.classList.toggle('text-red-500');
                    likeBtn.classList.toggle('text-white');
                    likeCount.textContent = currentCount.toLocaleString();
                }
            } catch (error) {
                console.error('❌ Failed to toggle like:', error);
                // Revert on error
                likeBtn.classList.toggle('text-red-500');
                likeBtn.classList.toggle('text-white');
                likeCount.textContent = currentCount.toLocaleString();
            }
        }

        // Share video functionality
        async function shareVideo(videoId) {
            console.log('Sharing video:', videoId);
            
            const shareCount = document.querySelector(`.share-count[data-video-id="${videoId}"]`);
            const currentCount = parseInt(shareCount.textContent.replace(/,/g, ''));
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Share count updated');
                    shareCount.textContent = (currentCount + 1).toLocaleString();
                    
                    // Show share options
                    const video = videos.find(v => v.id == videoId);
                    if (navigator.share) {
                        navigator.share({
                            title: video.title,
                            text: video.description,
                            url: video.youtube_url
                        });
                    } else {
                        // Fallback: copy to clipboard
                        navigator.clipboard.writeText(video.youtube_url);
                        alert('Link video telah disalin ke clipboard!');
                    }
                }
            } catch (error) {
                console.error('❌ Failed to update share count:', error);
            }
        }

        // Auto-increment view count when video becomes visible
        function incrementViewOnVisible() {
            const videoItems = document.querySelectorAll('.video-item');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const videoId = entry.target.dataset.videoId;
                        if (videoId) {
                            setTimeout(() => handleVideoClick(videoId), 1000);
                        }
                    }
                });
            }, { threshold: 0.5 });

            videoItems.forEach(item => observer.observe(item));
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Video page loaded with', videos.length, 'videos');
            incrementViewOnVisible();
        });
    </script>
</body>
</html>
