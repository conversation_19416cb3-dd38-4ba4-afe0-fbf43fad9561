<?php
require_once __DIR__ . '/../admin/config.php';

// Get database connection
$pdo = getConnection();

// Get published videos
try {
    $stmt = $pdo->prepare("
        SELECT
            id, title, description, content, youtube_url, youtube_id,
            thumbnail, category, tags, duration, status, views, likes,
            shares, comments_count, featured, created_at, updated_at,
            'admin' as username
        FROM videos
        WHERE status = 'published'
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $videos = [];
    error_log("Error fetching videos: " . $e->getMessage());
}

// Extract YouTube ID from URL
function extractYouTubeId($url) {
    if (!$url) return null;
    preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
    return isset($matches[1]) ? $matches[1] : 'dQw4w9WgXcQ';
}

// Generate YouTube embed URL with sound enabled
function getYouTubeEmbedUrl($videoId, $autoplay = false) {
    $params = http_build_query([
        'autoplay' => $autoplay ? '1' : '0',
        'mute' => '0', // Enable sound
        'controls' => '1',
        'loop' => '1',
        'playlist' => $videoId,
        'rel' => '0',
        'showinfo' => '0',
        'modestbranding' => '1',
        'iv_load_policy' => '3',
        'fs' => '1',
        'disablekb' => '0',
        'enablejsapi' => '1'
    ]);
    return "https://www.youtube.com/embed/{$videoId}?{$params}";
}
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video - News App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .video-container {
            aspect-ratio: 9/16;
        }
        .video-scroll {
            scroll-snap-type: y mandatory;
            overflow-y: scroll;
            height: 100vh;
        }
        .video-item {
            scroll-snap-align: start;
            height: 100vh;
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        /* Desktop: Center video with max width, hide bottom nav */
        @media (min-width: 769px) {
            .video-container {
                max-width: 450px;
                height: 80vh;
            }
            .bottom-nav {
                display: none;
            }
            .desktop-actions {
                right: -80px;
                top: 50%;
                transform: translateY(-50%);
                bottom: auto;
            }
            .desktop-info {
                bottom: 20px;
                right: -400px;
                width: 350px;
                left: auto;
            }
        }

        /* Mobile: Full screen video */
        @media (max-width: 768px) {
            .video-container {
                width: 100vw;
                height: 100vh;
                max-width: none;
                aspect-ratio: unset;
            }
            .desktop-actions {
                right: 16px;
                bottom: 128px;
                top: auto;
                transform: none;
            }
            .desktop-info {
                bottom: 80px;
                left: 16px;
                right: 80px;
                width: auto;
            }
        }

        /* Animations */
        .like-animation {
            animation: likeScale 0.3s ease-in-out;
        }

        .save-animation {
            animation: saveRotate 0.4s ease-in-out;
        }

        @keyframes likeScale {
            0% { transform: scale(1); }
            50% { transform: scale(1.3); }
            100% { transform: scale(1); }
        }

        @keyframes saveRotate {
            0% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(10deg) scale(1.1); }
            100% { transform: rotate(0deg) scale(1); }
        }

        .action-btn {
            transition: all 0.2s ease-in-out;
        }

        .action-btn:hover {
            transform: scale(1.1);
            background-color: rgba(255, 255, 255, 0.2);
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        /* Comment Modal */
        .comment-modal {
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
        }

        .comment-modal.show {
            transform: translateY(0);
        }

        /* Play Button */
        .play-button {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease-in-out;
        }

        .video-paused .play-button {
            opacity: 1;
            transform: scale(1);
        }

        .play-button:hover {
            transform: scale(1.1);
        }

        /* Desktop specific styles */
        @media (min-width: 769px) {
            .video-item {
                padding: 20px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .desktop-wrapper {
                display: flex;
                max-width: 1200px;
                width: 100%;
                gap: 40px;
                align-items: flex-start;
            }




        }
    </style>
</head>
<body class="bg-black text-white overflow-hidden">
    <div class="video-scroll" id="videoContainer">
        <?php if (empty($videos)): ?>
            <!-- Empty State -->
            <div class="video-item flex flex-col items-center justify-center h-screen">
                <div class="text-center">
                    <i class="fas fa-video text-8xl text-gray-500 mb-6"></i>
                    <h2 class="text-3xl font-bold mb-4 text-white">Maaf, Saat Ini Belum Ada Video</h2>
                    <p class="text-gray-400 text-lg mb-6 max-w-md mx-auto">
                        Video menarik akan segera hadir! Silakan kembali lagi nanti untuk menikmati konten video terbaru dari kami.
                    </p>
                    <a href="http://localhost:3000" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg transition-colors">
                        <i class="fas fa-home mr-2"></i>
                        Kembali ke Beranda
                    </a>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($videos as $index => $video): ?>
                <?php
                $youtubeId = $video['youtube_id'] ?: extractYouTubeId($video['youtube_url']);
                $embedUrl = getYouTubeEmbedUrl($youtubeId, $index === 0);
                ?>
                <div class="video-item relative bg-black flex items-center justify-center" data-video-id="<?= $video['id'] ?>">
                    <!-- Search Icon (Top Right) -->
                    <div class="absolute top-6 right-6 z-50">
                        <button class="w-10 h-10 rounded-full bg-white bg-opacity-20 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-30 transition-all duration-200">
                            <i class="fas fa-search text-white text-lg"></i>
                        </button>
                    </div>

                    <!-- Video Container -->
                    <div class="w-full mx-auto video-container relative bg-gray-900 video-paused" data-video-id="<?= $video['id'] ?>">
                        <!-- YouTube Iframe -->
                        <iframe
                            src="<?= htmlspecialchars($embedUrl) ?>"
                            class="w-full h-full border-0 md:rounded-lg"
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                            allowfullscreen
                            title="<?= htmlspecialchars($video['title']) ?>"
                            onload="console.log('Video loaded: <?= $youtubeId ?>')"
                            onerror="console.error('Video failed to load: <?= $youtubeId ?>')"
                            id="video-<?= $video['id'] ?>"
                        ></iframe>

                        <!-- Play Button Overlay -->
                        <div class="play-button absolute inset-0 flex items-center justify-center z-15">
                            <div class="w-20 h-20 bg-black bg-opacity-50 backdrop-blur-sm rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-2xl ml-1"></i>
                            </div>
                        </div>

                        <!-- Video Overlay for Interaction -->
                        <div class="absolute inset-0 z-10 cursor-pointer" onclick="toggleVideoPlay(<?= $video['id'] ?>)"></div>

                        <!-- Desktop Right Side Actions -->
                        <div class="hidden md:flex absolute right-4 bottom-20 flex-col items-center space-y-4 z-20">
                            <!-- Profile -->
                            <div class="flex flex-col items-center">
                                <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center border-2 border-white relative">
                                    <i class="fas fa-user text-white text-base"></i>
                                </div>
                                <div class="w-5 h-5 rounded-full bg-red-500 flex items-center justify-center -mt-2 border border-white">
                                    <i class="fas fa-plus text-white text-xs"></i>
                                </div>
                            </div>

                            <!-- Like Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleLike(<?= $video['id'] ?>)"
                                    class="like-btn action-btn w-12 h-12 rounded-full bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-70 transition-all"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-heart text-white text-lg"></i>
                                </button>
                                <span class="text-white text-xs font-semibold mt-1 like-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['likes']) ?></span>
                            </div>

                            <!-- Comment Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="openCommentModal(<?= $video['id'] ?>)"
                                    class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-70 transition-all"
                                >
                                    <i class="fas fa-comment-dots text-white text-lg"></i>
                                </button>
                                <span class="text-white text-xs font-semibold mt-1"><?= number_format($video['comments_count']) ?></span>
                            </div>

                            <!-- Save Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleSave(<?= $video['id'] ?>)"
                                    class="save-btn action-btn w-12 h-12 rounded-full bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-70 transition-all"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-bookmark text-white text-lg"></i>
                                </button>
                                <span class="text-white text-xs font-semibold mt-1">Simpan</span>
                            </div>

                            <!-- Share Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="shareVideo(<?= $video['id'] ?>)"
                                    class="action-btn w-12 h-12 rounded-full bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center hover:bg-opacity-70 transition-all"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-share-alt text-white text-lg"></i>
                                </button>
                                <span class="text-white text-xs font-semibold mt-1 share-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['shares']) ?></span>
                            </div>
                        </div>

                        <!-- Desktop Bottom Info -->
                        <div class="hidden md:block absolute bottom-4 left-4 right-20 z-20">
                            <div class="text-white max-w-2xl">
                                <!-- Username -->
                                <div class="flex items-center mb-2">
                                    <span class="text-base font-bold">@<?= htmlspecialchars($video['username'] ?? 'user') ?></span>
                                </div>

                                <!-- Title -->
                                <h2 class="text-lg font-semibold mb-2 line-clamp-2"><?= htmlspecialchars($video['title']) ?></h2>

                                <!-- Description -->
                                <p class="text-sm opacity-90 mb-2 line-clamp-2"><?= htmlspecialchars($video['description'] ?: $video['content']) ?></p>

                                <!-- Stats -->
                                <div class="flex items-center space-x-4 text-xs opacity-80 mb-2">
                                    <span><i class="fas fa-eye mr-1"></i><?= number_format($video['views']) ?> tayangan</span>
                                    <span><i class="fas fa-calendar mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                                    <span><i class="fas fa-clock mr-1"></i><?= $video['duration'] ?: '03:00' ?></span>
                                </div>

                                <!-- Tags -->
                                <div class="flex flex-wrap gap-1">
                                    <?php
                                    $tags = explode(',', $video['tags'] ?? '');
                                    foreach($tags as $tag):
                                        $tag = trim($tag);
                                        if(!empty($tag)):
                                    ?>
                                        <span class="text-blue-300 hover:text-blue-200 cursor-pointer text-sm">#<?= htmlspecialchars($tag) ?></span>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                            </div>
                        </div>

                        <!-- Mobile Right Side Actions (TikTok Style) -->
                        <div class="md:hidden absolute right-4 bottom-32 flex flex-col items-center space-y-4 z-20">
                            <!-- Profile -->
                            <div class="flex flex-col items-center">
                                <div class="w-14 h-14 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mb-2 border-2 border-white">
                                    <i class="fas fa-user text-white text-lg"></i>
                                </div>
                                <div class="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center -mt-4 border-2 border-black">
                                    <i class="fas fa-plus text-white text-xs"></i>
                                </div>
                            </div>

                            <!-- Like Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleLike(<?= $video['id'] ?>)"
                                    class="like-btn action-btn w-14 h-14 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-2"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-heart text-white text-2xl"></i>
                                </button>
                                <span class="text-white text-xs font-semibold like-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['likes']) ?></span>
                            </div>

                            <!-- Comment Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="openCommentModal(<?= $video['id'] ?>)"
                                    class="action-btn w-14 h-14 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-2"
                                >
                                    <i class="fas fa-comment-dots text-white text-2xl"></i>
                                </button>
                                <span class="text-white text-xs font-semibold"><?= number_format($video['comments_count']) ?></span>
                            </div>

                            <!-- Share Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="shareVideo(<?= $video['id'] ?>)"
                                    class="action-btn w-14 h-14 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-2"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-share-alt text-white text-2xl"></i>
                                </button>
                                <span class="text-white text-xs font-semibold share-count" data-video-id="<?= $video['id'] ?>"><?= number_format($video['shares']) ?></span>
                            </div>

                            <!-- Save Button -->
                            <div class="flex flex-col items-center">
                                <button
                                    onclick="toggleSave(<?= $video['id'] ?>)"
                                    class="save-btn action-btn w-14 h-14 rounded-full bg-black bg-opacity-30 backdrop-blur-sm flex items-center justify-center mb-2"
                                    data-video-id="<?= $video['id'] ?>"
                                >
                                    <i class="fas fa-bookmark text-white text-2xl"></i>
                                </button>
                                <span class="text-white text-xs font-semibold">Simpan</span>
                            </div>
                        </div>



                        <!-- Mobile Bottom Info -->
                        <div class="md:hidden absolute bottom-24 left-4 right-20 z-20">
                            <div class="text-white">
                                <h2 class="text-lg font-bold mb-2 line-clamp-2"><?= htmlspecialchars($video['title']) ?></h2>
                                <p class="text-sm opacity-80 mb-2 line-clamp-2"><?= htmlspecialchars($video['description'] ?: $video['content']) ?></p>
                                <div class="flex items-center space-x-4 text-xs opacity-70">
                                    <span><i class="fas fa-eye mr-1"></i><?= number_format($video['views']) ?></span>
                                    <span><i class="fas fa-calendar mr-1"></i><?= date('d M Y', strtotime($video['created_at'])) ?></span>
                                </div>
                                <!-- Tags -->
                                <div class="flex flex-wrap gap-1 mt-2">
                                    <?php
                                    $tags = $video['tags'] ? explode(',', $video['tags']) : [$video['category']];
                                    foreach (array_slice($tags, 0, 3) as $tag):
                                        $tag = trim($tag);
                                        if ($tag):
                                    ?>
                                        <span class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">
                                            #<?= htmlspecialchars($tag) ?>
                                        </span>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                            </div>
                        </div>


                    </div>




                    <!-- Video Counter -->
                    <div class="absolute top-16 right-6 bg-black bg-opacity-50 backdrop-blur-sm text-white text-xs px-3 py-1 rounded-full z-30 font-medium">
                        <?= $index + 1 ?> / <?= count($videos) ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Bottom Navigation (Mobile Only) -->
    <div class="md:hidden bottom-nav fixed bottom-0 left-0 right-0 bg-gray-900 border-t border-gray-700 z-40">
        <div class="flex justify-around items-center py-2">
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Home</span>
            </a>
            <a href="#" class="flex flex-col items-center py-2 px-4 text-blue-500">
                <i class="fas fa-play text-xl mb-1"></i>
                <span class="text-xs">Video</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-search text-xl mb-1"></i>
                <span class="text-xs">Cari</span>
            </a>
            <a href="http://localhost:3000" class="flex flex-col items-center py-2 px-4 text-gray-400 hover:text-white transition-colors">
                <i class="fas fa-bookmark text-xl mb-1"></i>
                <span class="text-xs">Simpan</span>
            </a>
        </div>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="comment-modal fixed inset-x-0 bottom-0 bg-white rounded-t-3xl z-50 max-h-96">
        <div class="p-4">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Komentar</h3>
                <button onclick="closeCommentModal()" class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center">
                    <i class="fas fa-times text-gray-600"></i>
                </button>
            </div>

            <!-- Comments List -->
            <div id="commentsList" class="space-y-4 max-h-60 overflow-y-auto mb-4">
                <!-- Sample Comments -->
                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">User123</p>
                            <p class="text-sm text-gray-700">Video yang bagus! Sangat informatif.</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>2 jam lalu</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>12
                            </button>
                        </div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <div class="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">NewsLover</p>
                            <p class="text-sm text-gray-700">Terima kasih atas informasinya!</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>1 jam lalu</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>5
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Comment Input -->
            <div class="border-t pt-4 space-y-3">
                <!-- Name Input -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-gray-600 text-xs"></i>
                    </div>
                    <input
                        type="text"
                        placeholder="Nama Anda..."
                        class="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                        id="commentName"
                        maxlength="50"
                    >
                </div>

                <!-- Comment Input -->
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-comment text-white text-xs"></i>
                    </div>
                    <div class="flex-1 flex items-center space-x-2">
                        <input
                            type="text"
                            placeholder="Tulis komentar..."
                            class="flex-1 bg-gray-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                            id="commentInput"
                            maxlength="1000"
                        >
                        <button
                            onclick="addComment()"
                            class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors"
                        >
                            <i class="fas fa-paper-plane text-white text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentVideoIndex = 0;
        let currentVideoId = null;
        const videos = <?= json_encode($videos) ?>;

        console.log('Loaded videos:', videos.length);

        // Comment Modal Functions
        function openCommentModal(videoId) {
            currentVideoId = videoId;
            const modal = document.getElementById('commentModal');
            modal.classList.add('show');

            // Load comments for this video
            loadComments(videoId);
        }

        function closeCommentModal() {
            const modal = document.getElementById('commentModal');
            modal.classList.remove('show');
            currentVideoId = null;
        }

        function loadComments(videoId) {
            console.log('Loading comments for video:', videoId);

            fetch(`../admin/api/video_comments.php?video_id=${videoId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayComments(data.comments);
                    } else {
                        console.error('Error loading comments:', data.error);
                    }
                })
                .catch(error => {
                    console.error('Error loading comments:', error);
                });
        }

        function displayComments(comments) {
            const commentsList = document.getElementById('commentsList');
            commentsList.innerHTML = '';

            if (comments.length === 0) {
                commentsList.innerHTML = `
                    <div class="text-center text-gray-500 py-8">
                        <i class="fas fa-comment text-3xl mb-2"></i>
                        <p>Belum ada komentar. Jadilah yang pertama!</p>
                    </div>
                `;
                return;
            }

            comments.forEach(comment => {
                const commentElement = document.createElement('div');
                commentElement.className = 'flex space-x-3';
                commentElement.innerHTML = `
                    <div class="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-xs"></i>
                    </div>
                    <div class="flex-1">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2">
                            <p class="font-semibold text-sm text-gray-900">${escapeHtml(comment.name)}</p>
                            <p class="text-sm text-gray-700">${escapeHtml(comment.comment)}</p>
                        </div>
                        <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                            <span>${comment.time_ago}</span>
                            <button class="hover:text-blue-500">Balas</button>
                            <button class="hover:text-red-500">
                                <i class="fas fa-heart mr-1"></i>0
                            </button>
                        </div>
                    </div>
                `;
                commentsList.appendChild(commentElement);
            });
        }

        function addComment() {
            const nameInput = document.getElementById('commentName');
            const commentInput = document.getElementById('commentInput');
            const name = nameInput.value.trim();
            const comment = commentInput.value.trim();

            if (!comment || !currentVideoId) return;
            if (!name) {
                alert('Silakan masukkan nama Anda');
                nameInput.focus();
                return;
            }
            if (comment.length < 3) {
                alert('Komentar minimal 3 karakter');
                commentInput.focus();
                return;
            }

            // Add comment to UI immediately (optimistic update)
            const commentsList = document.getElementById('commentsList');
            const commentElement = document.createElement('div');
            commentElement.className = 'flex space-x-3';
            commentElement.innerHTML = `
                <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user text-white text-xs"></i>
                </div>
                <div class="flex-1">
                    <div class="bg-gray-100 rounded-2xl px-4 py-2">
                        <p class="font-semibold text-sm text-gray-900">${name}</p>
                        <p class="text-sm text-gray-700">${comment}</p>
                    </div>
                    <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                        <span>Baru saja</span>
                        <button class="hover:text-blue-500">Balas</button>
                        <button class="hover:text-red-500">
                            <i class="fas fa-heart mr-1"></i>0
                        </button>
                    </div>
                </div>
            `;

            commentsList.appendChild(commentElement);
            nameInput.value = '';
            commentInput.value = '';

            // Send comment to server
            fetch('../admin/api/video_comments.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    video_id: currentVideoId,
                    name: name,
                    email: '',
                    comment: comment
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear inputs
                    nameInput.value = '';
                    commentInput.value = '';

                    // Update comment count in UI
                    updateCommentCount(currentVideoId);

                    // Reload comments to show the new one
                    loadComments(currentVideoId);

                    console.log('Comment added successfully');
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error adding comment:', error);
                alert('Gagal menambahkan komentar. Silakan coba lagi.');
            });
        }

        function updateCommentCount(videoId) {
            // Update comment count in the UI
            const commentCounts = document.querySelectorAll(`[data-video-id="${videoId}"] + span`);
            commentCounts.forEach(count => {
                if (count.textContent && !isNaN(parseInt(count.textContent))) {
                    const currentCount = parseInt(count.textContent.replace(/[^\d]/g, ''));
                    count.textContent = (currentCount + 1).toLocaleString();
                }
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Show toast notification
        function showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-80 text-white px-4 py-2 rounded-full text-sm z-50';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transition = 'opacity 0.3s';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 2000);
        }

        // Toggle save functionality with animation
        function toggleSave(videoId) {
            console.log('Toggling save for video:', videoId);

            const saveBtnMobile = document.querySelector(`.save-btn[data-video-id="${videoId}"]`);
            const saveBtnDesktop = document.querySelector(`.save-btn-desktop[data-video-id="${videoId}"]`);

            let isSaved = false;

            if (saveBtnMobile) {
                const saveIcon = saveBtnMobile.querySelector('i');

                // Add animation
                saveBtnMobile.classList.add('save-animation');
                setTimeout(() => saveBtnMobile.classList.remove('save-animation'), 400);

                // Toggle save state
                isSaved = saveIcon.classList.contains('text-yellow-400');
                saveIcon.classList.toggle('text-yellow-400');
                saveIcon.classList.toggle('text-white');
            }

            if (saveBtnDesktop) {
                // Add animation
                saveBtnDesktop.classList.add('save-animation');
                setTimeout(() => saveBtnDesktop.classList.remove('save-animation'), 400);

                // Toggle save state
                isSaved = saveBtnDesktop.classList.contains('saved');
                saveBtnDesktop.classList.toggle('saved');
            }

            // Show feedback
            const message = isSaved ? 'Video dihapus dari simpanan' : 'Video disimpan';
            showToast(message);
        }

        // Toggle video play/pause
        function toggleVideoPlay(videoId) {
            const videoContainer = document.querySelector(`.video-container[data-video-id="${videoId}"]`);
            const playButton = videoContainer.querySelector('.play-button');
            const iframe = videoContainer.querySelector('iframe');

            if (videoContainer.classList.contains('video-paused')) {
                // Play video
                videoContainer.classList.remove('video-paused');
                playButton.style.opacity = '0';

                // Send play command to iframe (if YouTube API is available)
                try {
                    iframe.contentWindow.postMessage('{"event":"command","func":"playVideo","args":""}', '*');
                } catch (e) {
                    console.log('YouTube API not available');
                }

                // Increment view count
                handleVideoClick(videoId);
            } else {
                // Pause video
                videoContainer.classList.add('video-paused');
                playButton.style.opacity = '1';

                try {
                    iframe.contentWindow.postMessage('{"event":"command","func":"pauseVideo","args":""}', '*');
                } catch (e) {
                    console.log('YouTube API not available');
                }
            }
        }

        // Handle video click to increment views
        async function handleVideoClick(videoId) {
            console.log('Video clicked:', videoId);
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ View count updated');
                }
            } catch (error) {
                console.error('❌ Failed to update view count:', error);
            }
        }

        // Toggle like functionality with animation
        async function toggleLike(videoId) {
            console.log('Toggling like for video:', videoId);

            // Handle mobile like button
            const likeBtnMobile = document.querySelector(`.like-btn[data-video-id="${videoId}"]`);
            const likeBtnDesktop = document.querySelector(`.like-btn-desktop[data-video-id="${videoId}"]`);

            let likeIcon, likeCount, currentCount, isLiked;

            if (likeBtnMobile) {
                likeIcon = likeBtnMobile.querySelector('i');
                likeCount = document.querySelector(`.like-count[data-video-id="${videoId}"]`);

                // Add animation
                likeBtnMobile.classList.add('like-animation');
                setTimeout(() => likeBtnMobile.classList.remove('like-animation'), 300);

                // Optimistic update
                isLiked = likeIcon.classList.contains('text-red-500');
                likeIcon.classList.toggle('text-red-500');
                likeIcon.classList.toggle('text-white');

                currentCount = parseInt(likeCount.textContent.replace(/,/g, ''));
                likeCount.textContent = (isLiked ? currentCount - 1 : currentCount + 1).toLocaleString();
            }

            if (likeBtnDesktop) {
                const likeIconDesktop = likeBtnDesktop.querySelector('i');
                const likeCountDesktop = document.querySelector(`.like-count-desktop[data-video-id="${videoId}"]`);

                // Add animation
                likeBtnDesktop.classList.add('like-animation');
                setTimeout(() => likeBtnDesktop.classList.remove('like-animation'), 300);

                // Optimistic update
                isLiked = likeBtnDesktop.classList.contains('liked');
                likeBtnDesktop.classList.toggle('liked');

                currentCount = parseInt(likeCountDesktop.textContent.replace(/,/g, ''));
                likeCountDesktop.textContent = (isLiked ? currentCount - 1 : currentCount + 1).toLocaleString();
            }
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Like toggled successfully');
                    // Update with server response if available
                    if (data.likes !== undefined) {
                        likeCount.textContent = parseInt(data.likes).toLocaleString();
                    }
                } else {
                    // Revert on failure
                    if (likeBtnMobile && likeIcon && likeCount) {
                        likeIcon.classList.toggle('text-red-500');
                        likeIcon.classList.toggle('text-white');
                        likeCount.textContent = currentCount.toLocaleString();
                    }
                    if (likeBtnDesktop) {
                        likeBtnDesktop.classList.toggle('liked');
                        const likeCountDesktop = document.querySelector(`.like-count-desktop[data-video-id="${videoId}"]`);
                        likeCountDesktop.textContent = currentCount.toLocaleString();
                    }
                }
            } catch (error) {
                console.error('❌ Failed to toggle like:', error);
                // Revert on error
                if (likeBtnMobile && likeIcon && likeCount) {
                    likeIcon.classList.toggle('text-red-500');
                    likeIcon.classList.toggle('text-white');
                    likeCount.textContent = currentCount.toLocaleString();
                }
                if (likeBtnDesktop) {
                    likeBtnDesktop.classList.toggle('liked');
                    const likeCountDesktop = document.querySelector(`.like-count-desktop[data-video-id="${videoId}"]`);
                    likeCountDesktop.textContent = currentCount.toLocaleString();
                }
            }
        }



        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('commentModal');
            if (e.target === modal) {
                closeCommentModal();
            }
        });

        // Handle Enter key in comment input
        document.getElementById('commentInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addComment();
            }
        });

        // Share video functionality
        async function shareVideo(videoId) {
            console.log('Sharing video:', videoId);

            const shareCountMobile = document.querySelector(`.share-count[data-video-id="${videoId}"]`);
            const shareCountDesktop = document.querySelector(`.share-count-desktop[data-video-id="${videoId}"]`);

            let currentCount = 0;
            if (shareCountMobile) {
                currentCount = parseInt(shareCountMobile.textContent.replace(/,/g, ''));
            } else if (shareCountDesktop) {
                currentCount = parseInt(shareCountDesktop.textContent.replace(/,/g, ''));
            }
            
            try {
                const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `id=${videoId}`
                });

                const data = await response.json();
                if (data.success) {
                    console.log('✅ Share count updated');
                    const newCount = (currentCount + 1).toLocaleString();
                    if (shareCountMobile) shareCountMobile.textContent = newCount;
                    if (shareCountDesktop) shareCountDesktop.textContent = newCount;
                    
                    // Show share options
                    const video = videos.find(v => v.id == videoId);
                    if (navigator.share) {
                        navigator.share({
                            title: video.title,
                            text: video.description,
                            url: video.youtube_url
                        });
                    } else {
                        // Fallback: copy to clipboard
                        navigator.clipboard.writeText(video.youtube_url);
                        alert('Link video telah disalin ke clipboard!');
                    }
                }
            } catch (error) {
                console.error('❌ Failed to update share count:', error);
            }
        }

        // Auto-increment view count when video becomes visible
        function incrementViewOnVisible() {
            const videoItems = document.querySelectorAll('.video-item');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const videoId = entry.target.dataset.videoId;
                        if (videoId) {
                            setTimeout(() => handleVideoClick(videoId), 1000);
                        }
                    }
                });
            }, { threshold: 0.5 });

            videoItems.forEach(item => observer.observe(item));
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            const modal = document.getElementById('commentModal');
            if (e.target === modal) {
                closeCommentModal();
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Video page loaded with', videos.length, 'videos');
            incrementViewOnVisible();

            // Handle Enter key in comment input
            const commentInput = document.getElementById('commentInput');
            if (commentInput) {
                commentInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        addComment();
                    }
                });
            }
        });
    </script>
</body>
</html>
