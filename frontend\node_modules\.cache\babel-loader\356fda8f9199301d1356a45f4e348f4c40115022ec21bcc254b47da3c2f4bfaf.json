{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n    loadLikedStatus();\n  }, [video.id]);\n\n  // Increment view count when video becomes active\n  useEffect(() => {\n    if (isActive) {\n      const incrementView = async () => {\n        try {\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/x-www-form-urlencoded'\n            },\n            body: `id=${video.id}`\n          });\n          const data = await response.json();\n          if (data.success) {\n            console.log('✅ View count incremented for video:', video.id);\n            video.stats.views = data.views || video.stats.views + 1;\n          }\n        } catch (error) {\n          console.error('❌ Failed to increment view count:', error);\n        }\n      };\n\n      // Delay to avoid rapid increments\n      const timer = setTimeout(incrementView, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, video.id, video.stats]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    console.log('Generating YouTube embed URL for:', videoId, 'autoplay:', autoplay);\n    if (!videoId || videoId === 'dQw4w9WgXcQ') {\n      console.warn('⚠️ Invalid or default YouTube ID, using fallback');\n    }\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '1',\n      // Enable controls for debugging\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '1',\n      // Allow fullscreen\n      disablekb: '0' // Allow keyboard for debugging\n    });\n    const url = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n    console.log('Generated YouTube URL:', url);\n    return url;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Direct to localhost PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('View increment response:', data);\n      if (data.success) {\n        console.log('✅ View count updated successfully');\n        // Update local view count if needed\n        video.stats.views = data.views || video.stats.views + 1;\n      } else {\n        console.error('❌ Failed to update view count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Share response:', data);\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  console.log('🎬 Rendering VideoItem with video:', video.title, 'ID:', video.youtubeId, 'Active:', isActive);\n  if (!video || !video.youtubeId) {\n    console.error('❌ Invalid video data:', video);\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: '100%',\n        height: '100%',\n        bgcolor: 'red',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white'\n      },\n      children: \"Invalid Video Data\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: '100vh',\n      width: '100%',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 10,\n        left: 10,\n        color: 'lime',\n        fontSize: 14,\n        zIndex: 50,\n        bgcolor: 'rgba(0,0,0,0.8)',\n        p: 1,\n        borderRadius: 1\n      },\n      children: [video.title, \" | ID: \", video.youtubeId, \" | Active: \", isActive ? 'YES' : 'NO']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: isMobile ? '100%' : '400px',\n        height: isMobile ? '100%' : '700px',\n        bgcolor: '#333',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"iframe\", {\n        ref: iframeRef,\n        src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n        style: {\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer',\n          backgroundColor: '#000'\n        },\n        onClick: handleVideoClick,\n        allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n        allowFullScreen: true,\n        title: video.title,\n        onLoad: () => console.log('✅ Video iframe loaded successfully for:', video.youtubeId),\n        onError: () => console.error('❌ Video iframe failed to load for:', video.youtubeId)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 285,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 360,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 349,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 338,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 367,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 366,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 417,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 443,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 463,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 435,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"Xm5L4dCaQ3eydNKZfI9d1G4E50A=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        console.log('Data type:', typeof data);\n        console.log('Data.success:', data.success);\n        console.log('Data.data:', data.data);\n        console.log('Is data.data array:', Array.isArray(data.data));\n        if (data.success && Array.isArray(data.data)) {\n          console.log('Raw data from API:', data.data);\n          console.log('Number of videos in raw data:', data.data.length);\n\n          // Convert database video data to component format\n          const publishedVideos = data.data.filter(video => video.status === 'published');\n          console.log('Published videos after filter:', publishedVideos);\n          console.log('Number of published videos:', publishedVideos.length);\n          const dbVideos = publishedVideos.map(video => {\n            var _video$content, _video$category;\n            console.log('Processing video:', video);\n            const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n            console.log('YouTube ID:', youtubeId);\n            const processedVideo = {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: youtubeId,\n              title: video.title || 'Untitled Video',\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...' || 'No description available',\n              tags: video.tags && video.tags.trim() ? video.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: video.created_by || 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0,\n                views: parseInt(video.views) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video',\n              thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n            };\n            console.log('Processed video result:', processedVideo);\n            return processedVideo;\n          });\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            console.log('✅ Setting videos state with:', dbVideos.length, 'videos');\n            setVideos(dbVideos);\n            setCurrentVideoIndex(0); // Reset to first video\n            console.log('✅ Videos state updated successfully');\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n    const handleScroll = e => {\n      if (isScrolling) return;\n      isScrolling = true;\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n      setTimeout(() => {\n        isScrolling = false;\n      }, 300);\n    };\n    const handleTouchStart = e => {\n      startY = e.touches[0].clientY;\n    };\n    const handleTouchEnd = e => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n      if (Math.abs(diffY) > 50) {\n        // Minimum swipe distance\n        isScrolling = true;\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n        setTimeout(() => {\n          isScrolling = false;\n        }, 300);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n        sx: {\n          fontSize: 40,\n          opacity: 0.7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 689,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 679,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 1\n      },\n      children: \"Belum Ada Video\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 692,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        opacity: 0.7,\n        maxWidth: 300,\n        mb: 4\n      },\n      children: \"Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: reloadVideos,\n      sx: {\n        color: 'white',\n        bgcolor: 'rgba(255, 255, 255, 0.1)',\n        '&:hover': {\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        },\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-refresh\",\n        style: {\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 710,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 701,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        opacity: 0.5\n      },\n      children: \"Ketuk untuk memuat ulang\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 725,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 735,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 724,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 742,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 752,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 741,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 759,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 769,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 758,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 718,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 668,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 788,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 802,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 779,\n    columnNumber: 5\n  }, this);\n  console.log('VideoFeed render - Loading:', loading, 'Videos count:', videos.length);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        color: 'white',\n        fontSize: 12,\n        zIndex: 100,\n        bgcolor: 'rgba(255,0,0,0.7)',\n        p: 1,\n        textAlign: 'center'\n      },\n      children: [\"Loading: \", loading ? 'Yes' : 'No', \" | Videos: \", videos.length, \" | Current: \", currentVideoIndex]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 821,\n      columnNumber: 7\n    }, this), loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 837,\n      columnNumber: 9\n    }, this) : videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 50,\n          left: 10,\n          color: 'yellow',\n          fontSize: 14,\n          zIndex: 200,\n          bgcolor: 'rgba(0,0,0,0.8)',\n          p: 1\n        },\n        children: [\"Rendering \", videos.length, \" videos, current: \", currentVideoIndex]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 840,\n        columnNumber: 11\n      }, this), videos.map((video, index) => {\n        console.log(`Rendering video ${index}:`, video.title, 'isActive:', index === currentVideoIndex);\n        return /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '100%',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n            transition: 'transform 0.3s ease-in-out',\n            zIndex: index === currentVideoIndex ? 10 : 1\n          },\n          children: /*#__PURE__*/_jsxDEV(VideoItem, {\n            video: video,\n            isActive: index === currentVideoIndex\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 872,\n            columnNumber: 17\n          }, this)\n        }, video.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 856,\n          columnNumber: 15\n        }, this);\n      }), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 891,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 881,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 897,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 811,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "loadLikedStatus", "response", "fetch", "data", "json", "success", "Array", "isArray", "includes", "id", "error", "console", "incrementView", "method", "headers", "body", "log", "views", "timer", "setTimeout", "clearTimeout", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "warn", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "url", "toString", "handleVideoClick", "title", "message", "handleLike", "wasLiked", "prev", "liked", "handleSave", "handleShare", "shares", "navigator", "share", "text", "description", "videoUrl", "clipboard", "writeText", "alert", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "youtubeId", "sx", "width", "height", "bgcolor", "display", "alignItems", "justifyContent", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "overflow", "top", "left", "fontSize", "zIndex", "p", "borderRadius", "ref", "src", "style", "border", "cursor", "backgroundColor", "onClick", "allow", "allowFullScreen", "onLoad", "onError", "right", "bottom", "flexDirection", "gap", "author", "avatar", "verified", "mt", "fontWeight", "toFixed", "comments", "mb", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "extractYouTubeId", "regExp", "match", "length", "loadVideos", "ok", "Error", "status", "publishedVideos", "filter", "dbVideos", "_video$content", "_video$category", "youtube_id", "youtube_url", "processedVideo", "content", "substring", "trim", "split", "category", "toLowerCase", "name", "created_by", "parseInt", "comments_count", "created_at", "toISOString", "thumbnail", "startY", "isScrolling", "handleScroll", "e", "deltaY", "handleTouchStart", "touches", "clientY", "handleTouchEnd", "endY", "changedTouches", "diffY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "padding", "textAlign", "opacity", "variant", "max<PERSON><PERSON><PERSON>", "className", "disabled", "LoadingVideoState", "borderTop", "animation", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n\n    loadLikedStatus();\n  }, [video.id]);\n\n  // Increment view count when video becomes active\n  useEffect(() => {\n    if (isActive) {\n      const incrementView = async () => {\n        try {\n          const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/x-www-form-urlencoded',\n            },\n            body: `id=${video.id}`\n          });\n\n          const data = await response.json();\n          if (data.success) {\n            console.log('✅ View count incremented for video:', video.id);\n            video.stats.views = data.views || video.stats.views + 1;\n          }\n        } catch (error) {\n          console.error('❌ Failed to increment view count:', error);\n        }\n      };\n\n      // Delay to avoid rapid increments\n      const timer = setTimeout(incrementView, 1000);\n      return () => clearTimeout(timer);\n    }\n  }, [isActive, video.id, video.stats]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    console.log('Generating YouTube embed URL for:', videoId, 'autoplay:', autoplay);\n\n    if (!videoId || videoId === 'dQw4w9WgXcQ') {\n      console.warn('⚠️ Invalid or default YouTube ID, using fallback');\n    }\n\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '1', // Enable controls for debugging\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '1', // Allow fullscreen\n      disablekb: '0' // Allow keyboard for debugging\n    });\n    const url = `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n    console.log('Generated YouTube URL:', url);\n    return url;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Direct to localhost PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('View increment response:', data);\n\n      if (data.success) {\n        console.log('✅ View count updated successfully');\n        // Update local view count if needed\n        video.stats.views = data.views || video.stats.views + 1;\n      } else {\n        console.error('❌ Failed to update view count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Share response:', data);\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  console.log('🎬 Rendering VideoItem with video:', video.title, 'ID:', video.youtubeId, 'Active:', isActive);\n\n  if (!video || !video.youtubeId) {\n    console.error('❌ Invalid video data:', video);\n    return (\n      <Box sx={{\n        width: '100%',\n        height: '100%',\n        bgcolor: 'red',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white'\n      }}>\n        Invalid Video Data\n      </Box>\n    );\n  }\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: '100vh',\n      width: '100%',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden'\n    }}>\n      {/* Debug Info */}\n      <Box sx={{\n        position: 'absolute',\n        top: 10,\n        left: 10,\n        color: 'lime',\n        fontSize: 14,\n        zIndex: 50,\n        bgcolor: 'rgba(0,0,0,0.8)',\n        p: 1,\n        borderRadius: 1\n      }}>\n        {video.title} | ID: {video.youtubeId} | Active: {isActive ? 'YES' : 'NO'}\n      </Box>\n\n      {/* Video Container */}\n      <Box sx={{\n        width: isMobile ? '100%' : '400px',\n        height: isMobile ? '100%' : '700px',\n        bgcolor: '#333',\n        position: 'relative',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      }}>\n        {/* YouTube Video Iframe */}\n        <iframe\n          ref={iframeRef}\n          src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n          style={{\n            width: '100%',\n            height: '100%',\n            border: 'none',\n            cursor: 'pointer',\n            backgroundColor: '#000'\n          }}\n          onClick={handleVideoClick}\n          allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n          allowFullScreen\n          title={video.title}\n          onLoad={() => console.log('✅ Video iframe loaded successfully for:', video.youtubeId)}\n          onError={() => console.error('❌ Video iframe failed to load for:', video.youtubeId)}\n        />\n      </Box>\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('API Response:', data);\n        console.log('Data type:', typeof data);\n        console.log('Data.success:', data.success);\n        console.log('Data.data:', data.data);\n        console.log('Is data.data array:', Array.isArray(data.data));\n\n        if (data.success && Array.isArray(data.data)) {\n          console.log('Raw data from API:', data.data);\n          console.log('Number of videos in raw data:', data.data.length);\n\n          // Convert database video data to component format\n          const publishedVideos = data.data.filter(video => video.status === 'published');\n          console.log('Published videos after filter:', publishedVideos);\n          console.log('Number of published videos:', publishedVideos.length);\n\n          const dbVideos = publishedVideos\n            .map(video => {\n              console.log('Processing video:', video);\n              const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n              console.log('YouTube ID:', youtubeId);\n              const processedVideo = {\n                id: video.id,\n                videoUrl: video.youtube_url,\n                youtubeId: youtubeId,\n                title: video.title || 'Untitled Video',\n                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',\n                tags: (video.tags && video.tags.trim()) ? video.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [video.category?.toLowerCase() || 'video'],\n                author: {\n                  name: video.created_by || 'News Reporter',\n                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                  verified: true\n                },\n                stats: {\n                  likes: parseInt(video.likes) || 0,\n                  comments: parseInt(video.comments_count) || 0,\n                  shares: parseInt(video.shares) || 0,\n                  views: parseInt(video.views) || 0\n                },\n                uploadDate: video.created_at || new Date().toISOString(),\n                duration: video.duration || '03:00',\n                category: video.category || 'Video',\n                thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n              };\n              console.log('Processed video result:', processedVideo);\n              return processedVideo;\n            });\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            console.log('✅ Setting videos state with:', dbVideos.length, 'videos');\n            setVideos(dbVideos);\n            setCurrentVideoIndex(0); // Reset to first video\n            console.log('✅ Videos state updated successfully');\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n\n    const handleScroll = (e) => {\n      if (isScrolling) return;\n      isScrolling = true;\n\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n\n      setTimeout(() => { isScrolling = false; }, 300);\n    };\n\n    const handleTouchStart = (e) => {\n      startY = e.touches[0].clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n\n      if (Math.abs(diffY) > 50) { // Minimum swipe distance\n        isScrolling = true;\n\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n\n        setTimeout(() => { isScrolling = false; }, 300);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    }}>\n      <Box sx={{\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      }}>\n        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />\n      </Box>\n\n      <Typography variant=\"h6\" sx={{ mb: 1 }}>\n        Belum Ada Video\n      </Typography>\n\n      <Typography variant=\"body2\" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>\n        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\n      </Typography>\n\n      {/* Refresh Button */}\n      <IconButton\n        onClick={reloadVideos}\n        sx={{\n          color: 'white',\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },\n          mb: 3\n        }}\n      >\n        <i className=\"fas fa-refresh\" style={{ fontSize: 20 }} />\n      </IconButton>\n\n      <Typography variant=\"caption\" sx={{ opacity: 0.5 }}>\n        Ketuk untuk memuat ulang\n      </Typography>\n\n      {/* Placeholder UI Elements */}\n      <Box sx={{\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      }}>\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <FavoriteBorderIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  console.log('VideoFeed render - Loading:', loading, 'Videos count:', videos.length);\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      {/* Debug Info */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        color: 'white',\n        fontSize: 12,\n        zIndex: 100,\n        bgcolor: 'rgba(255,0,0,0.7)',\n        p: 1,\n        textAlign: 'center'\n      }}>\n        Loading: {loading ? 'Yes' : 'No'} | Videos: {videos.length} | Current: {currentVideoIndex}\n      </Box>\n\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos.length > 0 ? (\n        <>\n          <Box sx={{\n            position: 'absolute',\n            top: 50,\n            left: 10,\n            color: 'yellow',\n            fontSize: 14,\n            zIndex: 200,\n            bgcolor: 'rgba(0,0,0,0.8)',\n            p: 1\n          }}>\n            Rendering {videos.length} videos, current: {currentVideoIndex}\n          </Box>\n\n          {videos.map((video, index) => {\n            console.log(`Rendering video ${index}:`, video.title, 'isActive:', index === currentVideoIndex);\n            return (\n              <Box\n                key={video.id}\n                sx={{\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                  transition: 'transform 0.3s ease-in-out',\n                  zIndex: index === currentVideoIndex ? 10 : 1\n                }}\n              >\n                <VideoItem\n                  video={video}\n                  isActive={index === currentVideoIndex}\n                />\n              </Box>\n            );\n          })}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACApC,SAAS,CAAC,MAAM;IACd,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4FAA4F,CAAC;QAC1H,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5Cd,UAAU,CAACc,IAAI,CAACA,IAAI,CAACK,QAAQ,CAACxB,KAAK,CAACyB,EAAE,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDV,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChB,KAAK,CAACyB,EAAE,CAAC,CAAC;;EAEd;EACA9C,SAAS,CAAC,MAAM;IACd,IAAIsB,QAAQ,EAAE;MACZ,MAAM2B,aAAa,GAAG,MAAAA,CAAA,KAAY;QAChC,IAAI;UACF,MAAMX,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;YACxHW,MAAM,EAAE,MAAM;YACdC,OAAO,EAAE;cACP,cAAc,EAAE;YAClB,CAAC;YACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;UACtB,CAAC,CAAC;UAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;UAClC,IAAID,IAAI,CAACE,OAAO,EAAE;YAChBM,OAAO,CAACK,GAAG,CAAC,qCAAqC,EAAEhC,KAAK,CAACyB,EAAE,CAAC;YAC5DzB,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAGd,IAAI,CAACc,KAAK,IAAIjC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAG,CAAC;UACzD;QACF,CAAC,CAAC,OAAOP,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QAC3D;MACF,CAAC;;MAED;MACA,MAAMQ,KAAK,GAAGC,UAAU,CAACP,aAAa,EAAE,IAAI,CAAC;MAC7C,OAAO,MAAMQ,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACjC,QAAQ,EAAED,KAAK,CAACyB,EAAE,EAAEzB,KAAK,CAACU,KAAK,CAAC,CAAC;;EAErC;EACA,MAAM2B,WAAW,GAAGxB,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMyB,UAAU,GAAGzB,QAAQ,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAM0B,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxDd,OAAO,CAACK,GAAG,CAAC,mCAAmC,EAAEQ,OAAO,EAAE,WAAW,EAAEC,QAAQ,CAAC;IAEhF,IAAI,CAACD,OAAO,IAAIA,OAAO,KAAK,aAAa,EAAE;MACzCb,OAAO,CAACe,IAAI,CAAC,kDAAkD,CAAC;IAClE;IAEA,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCH,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BI,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MAAE;MACfC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAER,OAAO;MACjBS,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MAAE;MACTC,SAAS,EAAE,GAAG,CAAC;IACjB,CAAC,CAAC;IACF,MAAMC,GAAG,GAAG,iCAAiCf,OAAO,IAAIG,MAAM,CAACa,QAAQ,CAAC,CAAC,EAAE;IAC3E7B,OAAO,CAACK,GAAG,CAAC,wBAAwB,EAAEuB,GAAG,CAAC;IAC1C,OAAOA,GAAG;EACZ,CAAC;EAED,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACA9B,OAAO,CAACK,GAAG,CAAC,gBAAgB,EAAEhC,KAAK,CAAC0D,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,MAAMzC,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;QACxHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEb,IAAI,CAAC;MAE7C,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChBM,OAAO,CAACK,GAAG,CAAC,mCAAmC,CAAC;QAChD;QACAhC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAGd,IAAI,CAACc,KAAK,IAAIjC,KAAK,CAACU,KAAK,CAACuB,KAAK,GAAG,CAAC;MACzD,CAAC,MAAM;QACLN,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEP,IAAI,CAACwC,OAAO,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,MAAMkC,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGzD,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAACqD,IAAI,IAAI1D,OAAO,GAAG0D,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACFnC,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEhC,KAAK,CAACyB,EAAE,CAAC;;MAEjD;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,uFAAuF,EAAE;QACpHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,uBAAuB,EAAEb,IAAI,CAAC;MAE1C,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACAhB,UAAU,CAACc,IAAI,CAAC4C,KAAK,IAAI,CAACF,QAAQ,CAAC;QACnCpD,QAAQ,CAACU,IAAI,CAACX,KAAK,KAAKqD,QAAQ,GAAGrD,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC;QAC1DmB,OAAO,CAACK,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLL,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEP,IAAI,CAACwC,OAAO,CAAC;QACjE;QACAtD,UAAU,CAACwD,QAAQ,CAAC;QACpBpD,QAAQ,CAACqD,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAOpC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACArB,UAAU,CAACwD,QAAQ,CAAC;MACpBpD,QAAQ,CAACqD,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBzD,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAM2D,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BtC,OAAO,CAACK,GAAG,CAAC,gBAAgB,EAAEhC,KAAK,CAACyB,EAAE,CAAC;IAEvC,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;QACxHW,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAM/B,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAEb,IAAI,CAAC;MAEpC,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACArB,KAAK,CAACU,KAAK,CAACwD,MAAM,GAAG/C,IAAI,CAAC+C,MAAM,IAAIlE,KAAK,CAACU,KAAK,CAACwD,MAAM,GAAG,CAAC;QAC1DvC,OAAO,CAACK,GAAG,CAAC,oCAAoC,CAAC;;QAEjD;QACA,IAAImC,SAAS,CAACC,KAAK,EAAE;UACnBD,SAAS,CAACC,KAAK,CAAC;YACdV,KAAK,EAAE1D,KAAK,CAAC0D,KAAK;YAClBW,IAAI,EAAErE,KAAK,CAACsE,WAAW;YACvBf,GAAG,EAAEvD,KAAK,CAACuE;UACb,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAJ,SAAS,CAACK,SAAS,CAACC,SAAS,CAACzE,KAAK,CAACuE,QAAQ,CAAC;UAC7CG,KAAK,CAAC,wCAAwC,CAAC;QACjD;MACF,CAAC,MAAM;QACL/C,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEP,IAAI,CAACwC,OAAO,CAAC;MAChE;IACF,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMiD,aAAa,GAAGA,CAAA,KAAM;IAC1BhD,OAAO,CAACK,GAAG,CAAC,0BAA0B,EAAEhC,KAAK,CAACyB,EAAE,CAAC;EACnD,CAAC;EAED,MAAMmD,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAEDzD,OAAO,CAACK,GAAG,CAAC,oCAAoC,EAAEhC,KAAK,CAAC0D,KAAK,EAAE,KAAK,EAAE1D,KAAK,CAACsF,SAAS,EAAE,SAAS,EAAErF,QAAQ,CAAC;EAE3G,IAAI,CAACD,KAAK,IAAI,CAACA,KAAK,CAACsF,SAAS,EAAE;IAC9B3D,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAE1B,KAAK,CAAC;IAC7C,oBACEJ,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,KAAK,EAAE;MACT,CAAE;MAAAC,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEvG,OAAA,CAAChB,GAAG;IAAC2G,EAAE,EAAE;MACPa,QAAQ,EAAE,UAAU;MACpBX,MAAM,EAAE,OAAO;MACfD,KAAK,EAAE,MAAM;MACbE,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBQ,QAAQ,EAAE;IACZ,CAAE;IAAAN,QAAA,gBAEAnG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPa,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,EAAE;QACPC,IAAI,EAAE,EAAE;QACRT,KAAK,EAAE,MAAM;QACbU,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,EAAE;QACVf,OAAO,EAAE,iBAAiB;QAC1BgB,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE;MAChB,CAAE;MAAAZ,QAAA,GACC/F,KAAK,CAAC0D,KAAK,EAAC,SAAO,EAAC1D,KAAK,CAACsF,SAAS,EAAC,aAAW,EAACrF,QAAQ,GAAG,KAAK,GAAG,IAAI;IAAA;MAAA+F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrE,CAAC,eAGNvG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPC,KAAK,EAAE3E,QAAQ,GAAG,MAAM,GAAG,OAAO;QAClC4E,MAAM,EAAE5E,QAAQ,GAAG,MAAM,GAAG,OAAO;QACnC6E,OAAO,EAAE,MAAM;QACfU,QAAQ,EAAE,UAAU;QACpBT,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MAAAE,QAAA,eAEAnG,OAAA;QACEgH,GAAG,EAAEjG,SAAU;QACfkG,GAAG,EAAEtE,kBAAkB,CAACvC,KAAK,CAACsF,SAAS,EAAErF,QAAQ,CAAE;QACnD6G,KAAK,EAAE;UACLtB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdsB,MAAM,EAAE,MAAM;UACdC,MAAM,EAAE,SAAS;UACjBC,eAAe,EAAE;QACnB,CAAE;QACFC,OAAO,EAAEzD,gBAAiB;QAC1B0D,KAAK,EAAC,0FAA0F;QAChGC,eAAe;QACf1D,KAAK,EAAE1D,KAAK,CAAC0D,KAAM;QACnB2D,MAAM,EAAEA,CAAA,KAAM1F,OAAO,CAACK,GAAG,CAAC,yCAAyC,EAAEhC,KAAK,CAACsF,SAAS,CAAE;QACtFgC,OAAO,EAAEA,CAAA,KAAM3F,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAE1B,KAAK,CAACsF,SAAS;MAAE;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPa,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPgB,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTf,MAAM,EAAE,CAAC;QACTO,MAAM,EAAE;MACV,CAAE;MACFE,OAAO,EAAEzD;IAAiB;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFvG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPa,QAAQ,EAAE,UAAU;QACpBmB,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACX7B,OAAO,EAAE,MAAM;QACf8B,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNjB,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,gBAEAnG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACb,MAAM;UACL8H,GAAG,EAAE7G,KAAK,CAAC2H,MAAM,CAACC,MAAO;UACzBrC,EAAE,EAAE;YACFC,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVsB,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDnG,KAAK,CAAC2H,MAAM,CAACE,QAAQ,iBACpBjI,OAAA,CAAChB,GAAG;UAAC2G,EAAE,EAAE;YACPG,OAAO,EAAE,SAAS;YAClBiB,YAAY,EAAE,KAAK;YACnBnB,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVE,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBiC,EAAE,EAAE,CAAC,CAAC;YACNf,MAAM,EAAE;UACV,CAAE;UAAAhB,QAAA,eACAnG,OAAA,CAACf,UAAU;YAAC0G,EAAE,EAAE;cAAEO,KAAK,EAAE,OAAO;cAAEU,QAAQ,EAAE,EAAE;cAAEuB,UAAU,EAAE;YAAO,CAAE;YAAAhC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACToI,OAAO,EAAEtD,UAAW;UACpB2B,EAAE,EAAE;YACFO,KAAK,EAAE1F,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCsF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAED3F,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvG,OAAA,CAACR,kBAAkB;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,EACvDvF,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEwH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGxH;QAAK;UAAAwF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACToI,OAAO,EAAEvC,aAAc;UACvBY,EAAE,EAAE;YACFO,KAAK,EAAE,OAAO;YACdJ,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFnG,OAAA,CAACP,qBAAqB;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,EACvD/F,KAAK,CAACU,KAAK,CAACuH;QAAQ;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACToI,OAAO,EAAElD,UAAW;UACpBuB,EAAE,EAAE;YACFO,KAAK,EAAExF,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCoF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,EAEDzF,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGvG,OAAA,CAACN,kBAAkB;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,EAAC;QAE3D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACToI,OAAO,EAAEjD,WAAY;UACrBsB,EAAE,EAAE;YACFO,KAAK,EAAE,OAAO;YACdJ,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAK,QAAA,eAEFnG,OAAA,CAACJ,SAAS;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE;UAAI,CAAE;UAAA/B,QAAA,EACvD/F,KAAK,CAACU,KAAK,CAACwD;QAAM;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPa,QAAQ,EAAE,UAAU;QACpBoB,MAAM,EAAE,EAAE;QACVjB,IAAI,EAAE,EAAE;QACRgB,KAAK,EAAE,EAAE;QACTd,MAAM,EAAE;MACV,CAAE;MAAAV,QAAA,gBAEAnG,OAAA,CAACf,UAAU;QAAC0G,EAAE,EAAE;UACdO,KAAK,EAAE,0BAA0B;UACjCU,QAAQ,EAAE,EAAE;UACZ0B,EAAE,EAAE;QACN,CAAE;QAAAnC,QAAA,GACCnB,UAAU,CAAC5E,KAAK,CAACmI,UAAU,CAAC,EAAC,UAAG,EAACnI,KAAK,CAACoI,QAAQ;MAAA;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbvG,OAAA,CAACf,UAAU;QAAC0G,EAAE,EAAE;UACdO,KAAK,EAAE,OAAO;UACdiC,UAAU,EAAE,MAAM;UAClBvB,QAAQ,EAAE,EAAE;UACZ0B,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE;QACd,CAAE;QAAAtC,QAAA,EACC/F,KAAK,CAAC0D;MAAK;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbvG,OAAA,CAACf,UAAU;QAAC0G,EAAE,EAAE;UACdO,KAAK,EAAE,0BAA0B;UACjCU,QAAQ,EAAE,EAAE;UACZ0B,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE,GAAG;UACf1C,OAAO,EAAE,aAAa;UACtB2C,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BlC,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EACC/F,KAAK,CAACsE;MAAW;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE+B,GAAG,EAAE,CAAC;UAAEc,QAAQ,EAAE;QAAO,CAAE;QAAAzC,QAAA,EACpD/F,KAAK,CAACyI,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzBhJ,OAAA,CAACZ,IAAI;UAEH6J,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZvD,EAAE,EAAE;YACFG,OAAO,EAAE,0BAA0B;YACnCI,KAAK,EAAE,OAAO;YACdU,QAAQ,EAAE,EAAE;YACZf,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEC,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGkD,KAAK;UAAA5C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAhG,EAAA,CA3dSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAA6J,EAAA,GANvBhJ,SAAS;AA4dlB,eAAe,SAASiJ,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1K,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC2K,MAAM,EAAEC,SAAS,CAAC,GAAG5K,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6K,OAAO,EAAEC,UAAU,CAAC,GAAG9K,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAM+K,gBAAgB,GAAIjG,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMkG,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAGnG,GAAG,CAACmG,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACC,MAAM,KAAK,EAAE,GAAID,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACA/K,SAAS,CAAC,MAAM;IACd,MAAMiL,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BL,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF5H,OAAO,CAACK,GAAG,CAAC,iCAAiC,CAAC;;QAE9C;QACA,MAAMf,QAAQ,GAAG,MAAMC,KAAK,CAAC,gFAAgF,CAAC;QAE9G,IAAI,CAACD,QAAQ,CAAC4I,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuB7I,QAAQ,CAAC8I,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAM5I,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCO,OAAO,CAACK,GAAG,CAAC,eAAe,EAAEb,IAAI,CAAC;QAClCQ,OAAO,CAACK,GAAG,CAAC,YAAY,EAAE,OAAOb,IAAI,CAAC;QACtCQ,OAAO,CAACK,GAAG,CAAC,eAAe,EAAEb,IAAI,CAACE,OAAO,CAAC;QAC1CM,OAAO,CAACK,GAAG,CAAC,YAAY,EAAEb,IAAI,CAACA,IAAI,CAAC;QACpCQ,OAAO,CAACK,GAAG,CAAC,qBAAqB,EAAEV,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,CAAC;QAE5D,IAAIA,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5CQ,OAAO,CAACK,GAAG,CAAC,oBAAoB,EAAEb,IAAI,CAACA,IAAI,CAAC;UAC5CQ,OAAO,CAACK,GAAG,CAAC,+BAA+B,EAAEb,IAAI,CAACA,IAAI,CAACwI,MAAM,CAAC;;UAE9D;UACA,MAAMK,eAAe,GAAG7I,IAAI,CAACA,IAAI,CAAC8I,MAAM,CAACjK,KAAK,IAAIA,KAAK,CAAC+J,MAAM,KAAK,WAAW,CAAC;UAC/EpI,OAAO,CAACK,GAAG,CAAC,gCAAgC,EAAEgI,eAAe,CAAC;UAC9DrI,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAEgI,eAAe,CAACL,MAAM,CAAC;UAElE,MAAMO,QAAQ,GAAGF,eAAe,CAC7BtB,GAAG,CAAC1I,KAAK,IAAI;YAAA,IAAAmK,cAAA,EAAAC,eAAA;YACZzI,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAEhC,KAAK,CAAC;YACvC,MAAMsF,SAAS,GAAGtF,KAAK,CAACqK,UAAU,IAAIb,gBAAgB,CAACxJ,KAAK,CAACsK,WAAW,CAAC;YACzE3I,OAAO,CAACK,GAAG,CAAC,aAAa,EAAEsD,SAAS,CAAC;YACrC,MAAMiF,cAAc,GAAG;cACrB9I,EAAE,EAAEzB,KAAK,CAACyB,EAAE;cACZ8C,QAAQ,EAAEvE,KAAK,CAACsK,WAAW;cAC3BhF,SAAS,EAAEA,SAAS;cACpB5B,KAAK,EAAE1D,KAAK,CAAC0D,KAAK,IAAI,gBAAgB;cACtCY,WAAW,EAAEtE,KAAK,CAACsE,WAAW,IAAI,EAAA6F,cAAA,GAAAnK,KAAK,CAACwK,OAAO,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK,IAAI,0BAA0B;cACxGhC,IAAI,EAAGzI,KAAK,CAACyI,IAAI,IAAIzI,KAAK,CAACyI,IAAI,CAACiC,IAAI,CAAC,CAAC,GAAI1K,KAAK,CAACyI,IAAI,CAACkC,KAAK,CAAC,GAAG,CAAC,CAACjC,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC+B,IAAI,CAAC,CAAC,CAAC,CAACT,MAAM,CAACtB,GAAG,IAAIA,GAAG,CAAC,GAAG,CAAC,EAAAyB,eAAA,GAAApK,KAAK,CAAC4K,QAAQ,cAAAR,eAAA,uBAAdA,eAAA,CAAgBS,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cACtJlD,MAAM,EAAE;gBACNmD,IAAI,EAAE9K,KAAK,CAAC+K,UAAU,IAAI,eAAe;gBACzCnD,MAAM,EAAE,gCAAgC,IAAI5H,KAAK,CAACyB,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9DoG,QAAQ,EAAE;cACZ,CAAC;cACDnH,KAAK,EAAE;gBACLF,KAAK,EAAEwK,QAAQ,CAAChL,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCyH,QAAQ,EAAE+C,QAAQ,CAAChL,KAAK,CAACiL,cAAc,CAAC,IAAI,CAAC;gBAC7C/G,MAAM,EAAE8G,QAAQ,CAAChL,KAAK,CAACkE,MAAM,CAAC,IAAI,CAAC;gBACnCjC,KAAK,EAAE+I,QAAQ,CAAChL,KAAK,CAACiC,KAAK,CAAC,IAAI;cAClC,CAAC;cACDkG,UAAU,EAAEnI,KAAK,CAACkL,UAAU,IAAI,IAAInG,IAAI,CAAC,CAAC,CAACoG,WAAW,CAAC,CAAC;cACxD/C,QAAQ,EAAEpI,KAAK,CAACoI,QAAQ,IAAI,OAAO;cACnCwC,QAAQ,EAAE5K,KAAK,CAAC4K,QAAQ,IAAI,OAAO;cACnCQ,SAAS,EAAE,8BAA8B9F,SAAS;YACpD,CAAC;YACD3D,OAAO,CAACK,GAAG,CAAC,yBAAyB,EAAEuI,cAAc,CAAC;YACtD,OAAOA,cAAc;UACvB,CAAC,CAAC;UAEJ5I,OAAO,CAACK,GAAG,CAAC,mBAAmB,EAAEkI,QAAQ,CAAC;;UAE1C;UACA,IAAIA,QAAQ,CAACP,MAAM,GAAG,CAAC,EAAE;YACvBhI,OAAO,CAACK,GAAG,CAAC,8BAA8B,EAAEkI,QAAQ,CAACP,MAAM,EAAE,QAAQ,CAAC;YACtEN,SAAS,CAACa,QAAQ,CAAC;YACnBf,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC;YACzBxH,OAAO,CAACK,GAAG,CAAC,qCAAqC,CAAC;UACpD,CAAC,MAAM;YACLL,OAAO,CAACK,GAAG,CAAC,mDAAmD,CAAC;YAChEqH,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACL1H,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEP,IAAI,CAAC;UACnDkI,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAO3H,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACA2H,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDK,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAIN;EACAjL,SAAS,CAAC,MAAM;IACd,IAAIyK,MAAM,CAACO,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,IAAI0B,MAAM,GAAG,CAAC;IACd,IAAIC,WAAW,GAAG,KAAK;IAEvB,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIF,WAAW,EAAE;MACjBA,WAAW,GAAG,IAAI;MAElB,IAAIE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIvC,iBAAiB,GAAGE,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;QACzDR,oBAAoB,CAACrF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAI0H,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIvC,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAACrF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;MAEA3B,UAAU,CAAC,MAAM;QAAEmJ,WAAW,GAAG,KAAK;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC;IAED,MAAMI,gBAAgB,GAAIF,CAAC,IAAK;MAC9BH,MAAM,GAAGG,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC/B,CAAC;IAED,MAAMC,cAAc,GAAIL,CAAC,IAAK;MAC5B,IAAIF,WAAW,EAAE;MACjB,MAAMQ,IAAI,GAAGN,CAAC,CAACO,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;MACxC,MAAMI,KAAK,GAAGX,MAAM,GAAGS,IAAI;MAE3B,IAAI5G,IAAI,CAACC,GAAG,CAAC6G,KAAK,CAAC,GAAG,EAAE,EAAE;QAAE;QAC1BV,WAAW,GAAG,IAAI;QAElB,IAAIU,KAAK,GAAG,CAAC,IAAI9C,iBAAiB,GAAGE,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;UACtD;UACAR,oBAAoB,CAACrF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAIkI,KAAK,GAAG,CAAC,IAAI9C,iBAAiB,GAAG,CAAC,EAAE;UAC7C;UACAC,oBAAoB,CAACrF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC;QAEA3B,UAAU,CAAC,MAAM;UAAEmJ,WAAW,GAAG,KAAK;QAAE,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC;IAEDW,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEX,YAAY,CAAC;IAC9CU,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,CAAC;IACvDO,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEL,cAAc,CAAC;IAEnD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEZ,YAAY,CAAC;MACjDU,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;MAC1DO,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC3C,iBAAiB,EAAEE,MAAM,CAACO,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMyC,YAAY,GAAGA,CAAA,KAAM;IACzB7C,UAAU,CAAC,IAAI,CAAC;IAChB;IACA0C,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB3M,OAAA,CAAChB,GAAG;IAAC2G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfE,OAAO,EAAE,MAAM;MACf8B,aAAa,EAAE,QAAQ;MACvB7B,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfI,KAAK,EAAE,OAAO;MACd0G,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAE;IAAA1G,QAAA,gBACAnG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVkB,YAAY,EAAE,KAAK;QACnBjB,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBqC,EAAE,EAAE;MACN,CAAE;MAAAnC,QAAA,eACAnG,OAAA,CAACH,aAAa;QAAC8F,EAAE,EAAE;UAAEiB,QAAQ,EAAE,EAAE;UAAEkG,OAAO,EAAE;QAAI;MAAE;QAAA1G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENvG,OAAA,CAACf,UAAU;MAAC8N,OAAO,EAAC,IAAI;MAACpH,EAAE,EAAE;QAAE2C,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,EAAC;IAExC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbvG,OAAA,CAACf,UAAU;MAAC8N,OAAO,EAAC,OAAO;MAACpH,EAAE,EAAE;QAAEmH,OAAO,EAAE,GAAG;QAAEE,QAAQ,EAAE,GAAG;QAAE1E,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,EAAC;IAExE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbvG,OAAA,CAACd,UAAU;MACToI,OAAO,EAAEkF,YAAa;MACtB7G,EAAE,EAAE;QACFO,KAAK,EAAE,OAAO;QACdJ,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE;UAAEA,OAAO,EAAE;QAA2B,CAAC;QAClDwC,EAAE,EAAE;MACN,CAAE;MAAAnC,QAAA,eAEFnG,OAAA;QAAGiN,SAAS,EAAC,gBAAgB;QAAC/F,KAAK,EAAE;UAAEN,QAAQ,EAAE;QAAG;MAAE;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEbvG,OAAA,CAACf,UAAU;MAAC8N,OAAO,EAAC,SAAS;MAACpH,EAAE,EAAE;QAAEmH,OAAO,EAAE;MAAI,CAAE;MAAA3G,QAAA,EAAC;IAEpD;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbvG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPI,OAAO,EAAE,MAAM;QACf+B,GAAG,EAAE,CAAC;QACNI,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,gBAEAnG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACTgO,QAAQ;UACRvH,EAAE,EAAE;YACFO,KAAK,EAAE,OAAO;YACdJ,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEgH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA3G,QAAA,eAEFnG,OAAA,CAACR,kBAAkB;YAAA4G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE,GAAG;YAAE4E,OAAO,EAAE;UAAI,CAAE;UAAA3G,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACTgO,QAAQ;UACRvH,EAAE,EAAE;YACFO,KAAK,EAAE,OAAO;YACdJ,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEgH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA3G,QAAA,eAEFnG,OAAA,CAACP,qBAAqB;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE,GAAG;YAAE4E,OAAO,EAAE;UAAI,CAAE;UAAA3G,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE8B,aAAa,EAAE,QAAQ;UAAE7B,UAAU,EAAE;QAAS,CAAE;QAAAG,QAAA,gBAC1EnG,OAAA,CAACd,UAAU;UACTgO,QAAQ;UACRvH,EAAE,EAAE;YACFO,KAAK,EAAE,OAAO;YACdJ,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEgH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA3G,QAAA,eAEFnG,OAAA,CAACJ,SAAS;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbvG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE,EAAE;YAAEsB,EAAE,EAAE,GAAG;YAAE4E,OAAO,EAAE;UAAI,CAAE;UAAA3G,QAAA,EAAC;QAEzE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAM4G,iBAAiB,GAAGA,CAAA,kBACxBnN,OAAA,CAAChB,GAAG;IAAC2G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfE,OAAO,EAAE,MAAM;MACf8B,aAAa,EAAE,QAAQ;MACvB7B,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfI,KAAK,EAAE;IACT,CAAE;IAAAC,QAAA,gBACAnG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPC,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,EAAE;QACVkB,YAAY,EAAE,KAAK;QACnBI,MAAM,EAAE,iCAAiC;QACzCiG,SAAS,EAAE,iBAAiB;QAC5BC,SAAS,EAAE,yBAAyB;QACpC/E,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAEgF,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAAlH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAELvG,OAAA,CAACf,UAAU;MAAC8N,OAAO,EAAC,OAAO;MAAA5G,QAAA,EAAC;IAE5B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAEDxE,OAAO,CAACK,GAAG,CAAC,6BAA6B,EAAEsH,OAAO,EAAE,eAAe,EAAEF,MAAM,CAACO,MAAM,CAAC;EAEnF,oBACE/J,OAAA,CAAChB,GAAG;IAAC2G,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfY,QAAQ,EAAE,QAAQ;MAClBD,QAAQ,EAAE,UAAU;MACpBV,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAE,QAAA,gBAEAnG,OAAA,CAAChB,GAAG;MAAC2G,EAAE,EAAE;QACPa,QAAQ,EAAE,UAAU;QACpBE,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPgB,KAAK,EAAE,CAAC;QACRzB,KAAK,EAAE,OAAO;QACdU,QAAQ,EAAE,EAAE;QACZC,MAAM,EAAE,GAAG;QACXf,OAAO,EAAE,mBAAmB;QAC5BgB,CAAC,EAAE,CAAC;QACJ+F,SAAS,EAAE;MACb,CAAE;MAAA1G,QAAA,GAAC,WACQ,EAACuD,OAAO,GAAG,KAAK,GAAG,IAAI,EAAC,aAAW,EAACF,MAAM,CAACO,MAAM,EAAC,cAAY,EAACT,iBAAiB;IAAA;MAAAlD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC,EAELmD,OAAO,gBACN1J,OAAA,CAACmN,iBAAiB;MAAA/G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBiD,MAAM,CAACO,MAAM,GAAG,CAAC,gBACnB/J,OAAA,CAAAE,SAAA;MAAAiG,QAAA,gBACEnG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UACPa,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,EAAE;UACPC,IAAI,EAAE,EAAE;UACRT,KAAK,EAAE,QAAQ;UACfU,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAE,GAAG;UACXf,OAAO,EAAE,iBAAiB;UAC1BgB,CAAC,EAAE;QACL,CAAE;QAAAX,QAAA,GAAC,YACS,EAACqD,MAAM,CAACO,MAAM,EAAC,oBAAkB,EAACT,iBAAiB;MAAA;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EAELiD,MAAM,CAACV,GAAG,CAAC,CAAC1I,KAAK,EAAE4I,KAAK,KAAK;QAC5BjH,OAAO,CAACK,GAAG,CAAC,mBAAmB4G,KAAK,GAAG,EAAE5I,KAAK,CAAC0D,KAAK,EAAE,WAAW,EAAEkF,KAAK,KAAKM,iBAAiB,CAAC;QAC/F,oBACEtJ,OAAA,CAAChB,GAAG;UAEF2G,EAAE,EAAE;YACFa,QAAQ,EAAE,UAAU;YACpBE,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPf,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,MAAM;YACdE,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBqH,SAAS,EAAE,cAAc,CAACtE,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;YAC9DiE,UAAU,EAAE,4BAA4B;YACxC1G,MAAM,EAAEmC,KAAK,KAAKM,iBAAiB,GAAG,EAAE,GAAG;UAC7C,CAAE;UAAAnD,QAAA,eAEFnG,OAAA,CAACG,SAAS;YACRC,KAAK,EAAEA,KAAM;YACbC,QAAQ,EAAE2I,KAAK,KAAKM;UAAkB;YAAAlD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC;QAAC,GAlBGnG,KAAK,CAACyB,EAAE;UAAAuE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBV,CAAC;MAEV,CAAC,CAAC,eAGFvG,OAAA,CAAChB,GAAG;QAAC2G,EAAE,EAAE;UACPa,QAAQ,EAAE,UAAU;UACpBE,GAAG,EAAE,EAAE;UACPiB,KAAK,EAAE,EAAE;UACT7B,OAAO,EAAE,oBAAoB;UAC7BiB,YAAY,EAAE,CAAC;UACfyG,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACL5G,MAAM,EAAE;QACV,CAAE;QAAAV,QAAA,eACAnG,OAAA,CAACf,UAAU;UAAC0G,EAAE,EAAE;YAAEO,KAAK,EAAE,OAAO;YAAEU,QAAQ,EAAE;UAAG,CAAE;UAAAT,QAAA,GAC9CmD,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAACO,MAAM;QAAA;UAAA3D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEHvG,OAAA,CAAC2M,eAAe;MAAAvG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACnB;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC8C,GAAA,CAlZuBD,SAAS;AAAAsE,GAAA,GAATtE,SAAS;AAAA,IAAAD,EAAA,EAAAuE,GAAA;AAAAC,YAAA,CAAAxE,EAAA;AAAAwE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}