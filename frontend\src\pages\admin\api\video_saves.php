<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config.php';

// Get database connection
$pdo = getConnection();

// Get client IP address
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

// Create video_saves table if not exists
function createVideoSavesTable($pdo) {
    $sql = "
    CREATE TABLE IF NOT EXISTS `video_saves` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `video_id` int(11) NOT NULL,
        `user_id` int(11) DEFAULT NULL,
        `ip_address` varchar(45) NOT NULL,
        `user_agent` text DEFAULT NULL,
        `saved_at` timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (`id`),
        UNIQUE KEY `unique_video_save` (`video_id`, `ip_address`),
        KEY `video_id` (`video_id`),
        KEY `user_id` (`user_id`),
        KEY `ip_address` (`ip_address`),
        KEY `saved_at` (`saved_at`),
        FOREIGN KEY (`video_id`) REFERENCES `videos`(`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ";
    
    try {
        $pdo->exec($sql);
    } catch (Exception $e) {
        // Table might already exist or foreign key constraint issue
        error_log("Error creating video_saves table: " . $e->getMessage());
    }
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    // Ensure table exists
    createVideoSavesTable($pdo);
    
    switch ($method) {
        case 'GET':
            // Check if user has saved a video
            $videoId = $_GET['video_id'] ?? null;
            if (!$videoId) {
                throw new Exception('Video ID is required');
            }

            $ip = getClientIP();

            $stmt = $pdo->prepare("
                SELECT COUNT(*) as saved 
                FROM video_saves 
                WHERE video_id = ? AND ip_address = ?
            ");
            $stmt->execute([$videoId, $ip]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'saved' => $result['saved'] > 0
            ]);
            break;

        case 'POST':
            // Toggle save for a video
            $videoId = $input['video_id'] ?? null;
            if (!$videoId) {
                throw new Exception('Video ID is required');
            }

            $ip = getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if user has already saved this video
            $stmt = $pdo->prepare("
                SELECT id FROM video_saves 
                WHERE video_id = ? AND ip_address = ?
            ");
            $stmt->execute([$videoId, $ip]);
            $existingSave = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existingSave) {
                // Unsave - remove the save
                $stmt = $pdo->prepare("
                    DELETE FROM video_saves 
                    WHERE video_id = ? AND ip_address = ?
                ");
                $stmt->execute([$videoId, $ip]);

                $action = 'unsaved';
                $saved = false;
            } else {
                // Save - add new save
                $stmt = $pdo->prepare("
                    INSERT INTO video_saves (video_id, ip_address, user_agent, saved_at)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([$videoId, $ip, $userAgent]);

                $action = 'saved';
                $saved = true;
            }

            echo json_encode([
                'success' => true,
                'action' => $action,
                'saved' => $saved
            ]);
            break;

        default:
            throw new Exception('Method not allowed');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
