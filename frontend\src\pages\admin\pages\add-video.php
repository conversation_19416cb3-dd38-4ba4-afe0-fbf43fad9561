<!-- Add Video -->
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">Tambah Video</h1>
                <p class="text-gray-600 mt-1">Tambahkan video baru ke dalam sistem</p>
            </div>
            <a href="?page=videos" class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>Kembali
            </a>
        </div>
    </div>

    <form id="addVideoForm" enctype="multipart/form-data" class="space-y-6">
        <!-- Basic Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Dasar</h3>
            <div class="grid grid-cols-1 gap-6">
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                        Judul Video <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="title" name="title" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Masukkan judul video...">
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Deskripsi Singkat <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" name="description" rows="3" required
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Deskripsi singkat yang akan ditampilkan di halaman utama..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">Maksimal 200 karakter</p>
                </div>

                <div>
                    <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                        Konten Lengkap
                    </label>
                    <textarea id="content" name="content" rows="6"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Konten lengkap yang akan ditampilkan di halaman detail video..."></textarea>
                    <p class="text-sm text-gray-500 mt-1">Konten lengkap yang akan ditampilkan di halaman detail video</p>
                </div>
            </div>
        </div>

        <!-- Video Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Video</h3>

            <!-- Video Type Selection -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-3">Tipe Video <span class="text-red-500">*</span></label>
                <div class="flex space-x-4">
                    <label class="flex items-center">
                        <input type="radio" name="video_type" value="youtube" checked class="mr-2" onchange="toggleVideoType()">
                        <span class="text-sm">YouTube URL</span>
                    </label>
                    <label class="flex items-center">
                        <input type="radio" name="video_type" value="upload" class="mr-2" onchange="toggleVideoType()">
                        <span class="text-sm">Upload File Video</span>
                    </label>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- YouTube URL Section -->
                <div id="youtube-section" class="lg:col-span-2">
                    <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                        URL YouTube <span class="text-red-500">*</span>
                    </label>
                    <input type="url" id="youtube_url" name="youtube_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://www.youtube.com/watch?v=XXXXXXXXXXX">
                    <p class="text-sm text-gray-500 mt-1">Masukkan URL video YouTube (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)</p>
                </div>

                <!-- Video Upload Section -->
                <div id="upload-section" class="lg:col-span-2" style="display: none;">
                    <label for="video_file" class="block text-sm font-medium text-gray-700 mb-2">
                        Upload Video File <span class="text-red-500">*</span>
                    </label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="video_file" name="video_file" accept="video/*" class="hidden" onchange="previewVideoFile(this)">
                        <div id="video-upload-area" onclick="document.getElementById('video_file').click()" class="cursor-pointer">
                            <i class="fas fa-video text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload video</p>
                            <p class="text-sm text-gray-500 mt-1">Format yang didukung: MP4, AVI, MOV, WebM, MKV (Maksimal 100MB)</p>
                        </div>
                        <div id="video-preview" class="hidden mt-4">
                            <div class="bg-gray-100 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-video text-primary mr-2"></i>
                                        <span id="video-filename" class="text-sm font-medium"></span>
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        <span id="video-filesize"></span>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="bg-gray-200 rounded-full h-2">
                                        <div id="upload-progress" class="bg-primary h-2 rounded-full" style="width: 0%"></div>
                                    </div>
                                    <p id="upload-status" class="text-xs text-gray-500 mt-1">Siap untuk upload</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="thumbnail" class="block text-sm font-medium text-gray-700 mb-2">Thumbnail Video</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="thumbnail" name="thumbnail" accept="image/*" class="hidden" onchange="previewThumbnail(this)">
                        <div id="thumbnail-upload-area" onclick="document.getElementById('thumbnail').click()" class="cursor-pointer">
                            <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload thumbnail</p>
                            <p class="text-sm text-gray-500 mt-1">Jika tidak diisi, thumbnail akan diambil otomatis dari YouTube</p>
                        </div>
                    </div>
                </div>

                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">Kategori</label>
                    <select id="category" name="category"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="Umum">Umum</option>
                        <option value="Teknologi">Teknologi</option>
                        <option value="Bisnis">Bisnis</option>
                        <option value="Olahraga">Olahraga</option>
                        <option value="Hiburan">Hiburan</option>
                        <option value="Politik">Politik</option>
                        <option value="Kesehatan">Kesehatan</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Additional Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Pengaturan Tambahan</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input type="text" id="tags" name="tags"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="tag1, tag2, tag3">
                    <p class="text-sm text-gray-500 mt-1">Pisahkan tag dengan koma</p>
                </div>

                <div>
                    <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">Durasi (menit:detik)</label>
                    <input type="text" id="duration" name="duration"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="03:45">
                    <p class="text-sm text-gray-500 mt-1">Format: mm:ss (contoh: 03:45). Jika tidak diisi, durasi akan diambil dari YouTube</p>
                </div>

                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select id="status" name="status"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>

                <div class="flex items-center">
                    <div class="flex items-center h-5">
                        <input type="checkbox" id="featured" name="featured" value="1"
                               class="w-4 h-4 text-primary bg-gray-100 border-gray-300 rounded focus:ring-primary focus:ring-2">
                    </div>
                    <div class="ml-3 text-sm">
                        <label for="featured" class="font-medium text-gray-700">Tampilkan di Featured</label>
                        <p class="text-gray-500">Video akan ditampilkan di bagian featured</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- YouTube Preview -->
        <div id="preview-container" class="bg-white rounded-xl shadow-sm p-6" style="display: none;">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Preview YouTube</h3>
            <div class="aspect-w-16 aspect-h-9">
                <iframe id="youtube-preview" class="w-full h-64 rounded-lg" src="" allowfullscreen></iframe>
            </div>
        </div>

        <!-- Submit Buttons -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-end space-x-4">
                <a href="?page=videos" class="px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-times mr-2"></i>Batal
                </a>
                <button type="submit" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>Simpan Video
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Toggle between YouTube and Upload sections
function toggleVideoType() {
    const videoType = document.querySelector('input[name="video_type"]:checked').value;
    const youtubeSection = document.getElementById('youtube-section');
    const uploadSection = document.getElementById('upload-section');
    const youtubeUrl = document.getElementById('youtube_url');
    const videoFile = document.getElementById('video_file');

    if (videoType === 'youtube') {
        youtubeSection.style.display = 'block';
        uploadSection.style.display = 'none';
        youtubeUrl.required = true;
        videoFile.required = false;
    } else {
        youtubeSection.style.display = 'none';
        uploadSection.style.display = 'block';
        youtubeUrl.required = false;
        videoFile.required = true;
    }
}

// Preview video file
function previewVideoFile(input) {
    const file = input.files[0];
    if (!file) return;

    const uploadArea = document.getElementById('video-upload-area');
    const preview = document.getElementById('video-preview');
    const filename = document.getElementById('video-filename');
    const filesize = document.getElementById('video-filesize');
    const status = document.getElementById('upload-status');

    // Check file size (100MB limit)
    const maxSize = 100 * 1024 * 1024; // 100MB in bytes
    if (file.size > maxSize) {
        Swal.fire({
            icon: 'error',
            title: 'File Terlalu Besar',
            text: 'Ukuran file maksimal 100MB'
        });
        input.value = '';
        return;
    }

    // Check file type
    const allowedTypes = ['video/mp4', 'video/avi', 'video/mov', 'video/webm', 'video/mkv', 'video/quicktime'];
    if (!allowedTypes.includes(file.type)) {
        Swal.fire({
            icon: 'error',
            title: 'Format File Tidak Didukung',
            text: 'Format yang didukung: MP4, AVI, MOV, WebM, MKV'
        });
        input.value = '';
        return;
    }

    // Show preview
    uploadArea.style.display = 'none';
    preview.classList.remove('hidden');
    filename.textContent = file.name;
    filesize.textContent = formatFileSize(file.size);
    status.textContent = 'Siap untuk upload';
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Extract YouTube ID from URL
function getYouTubeId(url) {
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);
    return (match && match[2].length === 11) ? match[2] : null;
}

// Update YouTube preview when URL changes
document.getElementById('youtube_url').addEventListener('input', function() {
    const youtubeUrl = this.value;
    const youtubeId = getYouTubeId(youtubeUrl);
    const previewContainer = document.getElementById('preview-container');
    const youtubePreview = document.getElementById('youtube-preview');
    
    if (youtubeId) {
        youtubePreview.src = `https://www.youtube.com/embed/${youtubeId}`;
        previewContainer.style.display = 'block';
    } else {
        previewContainer.style.display = 'none';
    }
});

// Preview thumbnail function
function previewThumbnail(input) {
    const uploadArea = document.getElementById('thumbnail-upload-area');

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            uploadArea.innerHTML = `
                <div class="relative">
                    <img src="${e.target.result}" alt="Preview" class="max-w-full h-32 object-cover rounded-lg mx-auto">
                    <div class="mt-2">
                        <p class="text-sm text-gray-600">${input.files[0].name}</p>
                        <button type="button" onclick="removeThumbnail()" class="text-red-500 text-sm hover:text-red-700">
                            <i class="fas fa-times mr-1"></i>Hapus
                        </button>
                    </div>
                </div>
            `;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Remove thumbnail function
function removeThumbnail() {
    const input = document.getElementById('thumbnail');
    const uploadArea = document.getElementById('thumbnail-upload-area');

    input.value = '';
    uploadArea.innerHTML = `
        <i class="fas fa-image text-4xl text-gray-400 mb-2"></i>
        <p class="text-gray-600">Klik untuk upload thumbnail</p>
        <p class="text-sm text-gray-500 mt-1">Jika tidak diisi, thumbnail akan diambil otomatis dari YouTube</p>
    `;
}

// Form submission
document.getElementById('addVideoForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const videoType = document.querySelector('input[name="video_type"]:checked').value;

    // Validate based on video type
    if (videoType === 'youtube') {
        const youtubeUrl = document.getElementById('youtube_url').value;
        const youtubeId = getYouTubeId(youtubeUrl);
        if (!youtubeId) {
            Swal.fire({
                icon: 'error',
                title: 'URL YouTube Tidak Valid',
                text: 'Masukkan URL YouTube yang valid (contoh: https://www.youtube.com/watch?v=XXXXXXXXXXX)'
            });
            return;
        }
    } else {
        const videoFile = document.getElementById('video_file').files[0];
        if (!videoFile) {
            Swal.fire({
                icon: 'error',
                title: 'File Video Diperlukan',
                text: 'Pilih file video untuk diupload'
            });
            return;
        }
    }

    // Create FormData
    const formData = new FormData(this);
    formData.append('action', 'add_video');

    if (videoType === 'youtube') {
        const youtubeUrl = document.getElementById('youtube_url').value;
        const youtubeId = getYouTubeId(youtubeUrl);
        formData.append('youtube_id', youtubeId);
    }
    
    try {
        // Show loading with different message for upload
        const loadingTitle = videoType === 'upload' ? 'Mengupload Video...' : 'Menyimpan...';
        const loadingText = videoType === 'upload' ? 'Mohon tunggu, sedang mengupload file video' : 'Mohon tunggu sebentar';

        Swal.fire({
            title: loadingTitle,
            text: loadingText,
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading();
            }
        });

        // Update progress for file upload
        if (videoType === 'upload') {
            const progressBar = document.getElementById('upload-progress');
            const statusText = document.getElementById('upload-status');

            progressBar.style.width = '10%';
            statusText.textContent = 'Mengupload...';
        }

        // Send request
        const response = await fetch('api.php', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Berhasil!',
                text: 'Video berhasil ditambahkan',
                confirmButtonText: 'OK'
            }).then(() => {
                window.location.href = '?page=videos';
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Gagal!',
                text: data.message || 'Terjadi kesalahan saat menyimpan video'
            });
        }
    } catch (error) {
        console.error('Error:', error);
        Swal.fire({
            icon: 'error',
            title: 'Gagal!',
            text: 'Terjadi kesalahan saat menyimpan video'
        });
    }
});
</script>
