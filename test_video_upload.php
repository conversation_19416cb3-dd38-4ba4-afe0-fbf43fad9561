<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    echo "Testing video upload functionality...\n";
    
    // Insert a sample uploaded video record
    $stmt = $pdo->prepare("INSERT INTO videos (
        title, description, content, youtube_url, youtube_id,
        video_path, file_size, file_format, video_type,
        thumbnail, category, tags, duration, status,
        featured, created_at, updated_at
    ) VALUES (
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, NOW(), NOW()
    )");

    $result = $stmt->execute([
        'Sample Uploaded Video', // title
        'This is a sample video that was uploaded directly to the server', // description
        'Sample video content for testing HTML5 video player functionality', // content
        '', // youtube_url (empty for upload type)
        '', // youtube_id (empty for upload type)
        '/uploads/sample_video.mp4', // video_path
        15728640, // file_size (15MB in bytes)
        'mp4', // file_format
        'upload', // video_type
        '', // thumbnail
        'Technology', // category
        'sample,test,upload,html5', // tags
        '02:30', // duration
        'published', // status
        0 // featured
    ]);

    if ($result) {
        $videoId = $pdo->lastInsertId();
        echo "✓ Sample video record created with ID: $videoId\n";
        
        // Show current videos
        echo "\nCurrent videos in database:\n";
        $stmt = $pdo->query("SELECT id, title, video_type, video_path, file_size, file_format FROM videos ORDER BY created_at DESC LIMIT 5");
        $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($videos as $video) {
            $type = $video['video_type'] ?? 'youtube';
            $size = $video['file_size'] ? formatFileSize($video['file_size']) : 'N/A';
            $format = $video['file_format'] ?? 'N/A';
            $path = $video['video_path'] ?? 'N/A';
            
            echo "- ID: {$video['id']}, Title: {$video['title']}, Type: $type, Format: $format, Size: $size\n";
            if ($type === 'upload') {
                echo "  Path: $path\n";
            }
        }
        
    } else {
        echo "✗ Failed to create sample video record\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

function formatFileSize($bytes) {
    if ($bytes == 0) return '0 Bytes';
    $k = 1024;
    $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
    $i = floor(log($bytes) / log($k));
    return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
}
?>
