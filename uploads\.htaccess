# Allow video file access
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    Header set Content-Type video/mp4
    Header set Accept-Ranges bytes
</FilesMatch>

# Enable CORS for video files
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type"
</FilesMatch>

# Cache video files
<FilesMatch "\.(mp4|avi|mov|webm|mkv|flv|wmv|3gp|ogg)$">
    ExpiresActive On
    ExpiresDefault "access plus 1 month"
</FilesMatch>