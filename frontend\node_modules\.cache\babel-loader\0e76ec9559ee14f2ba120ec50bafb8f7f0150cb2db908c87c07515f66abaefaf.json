{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n    loadLikedStatus();\n  }, [video.id]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16',\n      // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        cursor: 'pointer',\n        objectFit: 'cover' // Maintain aspect ratio\n      },\n      onClick: handleVideoClick,\n      allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n      allowFullScreen: true,\n      title: video.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 217,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 262,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 328,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 319,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 230,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 365,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 185,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"VfDT3SvRyjPCeKKMgd+ZxiIgKCQ=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data.filter(video => video.status === 'published') // Only published videos\n          .map(video => {\n            var _video$content, _video$category;\n            const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n            return {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: youtubeId,\n              title: video.title || 'Untitled Video',\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...' || 'No description available',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: video.created_by || 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0,\n                views: parseInt(video.views) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video',\n              thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n            };\n          });\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n    const handleScroll = e => {\n      if (isScrolling) return;\n      isScrolling = true;\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n      setTimeout(() => {\n        isScrolling = false;\n      }, 300);\n    };\n    const handleTouchStart = e => {\n      startY = e.touches[0].clientY;\n    };\n    const handleTouchEnd = e => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n      if (Math.abs(diffY) > 50) {\n        // Minimum swipe distance\n        isScrolling = true;\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n        setTimeout(() => {\n          isScrolling = false;\n        }, 300);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n        sx: {\n          fontSize: 40,\n          opacity: 0.7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 573,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 563,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 1\n      },\n      children: \"Belum Ada Video\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 576,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        opacity: 0.7,\n        maxWidth: 300,\n        mb: 4\n      },\n      children: \"Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 580,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: reloadVideos,\n      sx: {\n        color: 'white',\n        bgcolor: 'rgba(255, 255, 255, 0.1)',\n        '&:hover': {\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        },\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-refresh\",\n        style: {\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        opacity: 0.5\n      },\n      children: \"Ketuk untuk memuat ulang\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 597,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 625,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 651,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 602,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 552,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 672,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 686,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 663,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 703,\n      columnNumber: 9\n    }, this) : videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n          transition: 'transform 0.3s ease-in-out'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoItem, {\n          video: video,\n          isActive: index === currentVideoIndex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 722,\n          columnNumber: 15\n        }, this)\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 707,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 730,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 746,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 693,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "loadLikedStatus", "response", "fetch", "data", "json", "success", "Array", "isArray", "includes", "id", "error", "console", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "toString", "handleVideoClick", "log", "title", "method", "expressError", "handleLike", "wasLiked", "prev", "liked", "handleSave", "handleShare", "shares", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "aspectRatio", "borderRadius", "children", "ref", "src", "youtubeId", "style", "border", "cursor", "objectFit", "onClick", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "right", "bottom", "zIndex", "flexDirection", "gap", "author", "avatar", "verified", "mt", "color", "fontSize", "fontWeight", "toFixed", "comments", "mb", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "description", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "loadVideos", "ok", "Error", "status", "dbVideos", "filter", "_video$content", "_video$category", "youtube_id", "extractYouTubeId", "youtube_url", "videoUrl", "content", "substring", "split", "trim", "category", "toLowerCase", "name", "created_by", "parseInt", "comments_count", "views", "created_at", "toISOString", "thumbnail", "length", "url", "regExp", "match", "startY", "isScrolling", "handleScroll", "e", "deltaY", "setTimeout", "handleTouchStart", "touches", "clientY", "handleTouchEnd", "endY", "changedTouches", "diffY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "padding", "textAlign", "opacity", "variant", "className", "disabled", "LoadingVideoState", "borderTop", "animation", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n\n    loadLikedStatus();\n  }, [video.id]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/like`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked);\n        setLikes(data.likes);\n      } else {\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Share video:', video.id);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      let response;\n      let data;\n\n      try {\n        response = await fetch(`http://localhost:3000/api/videos/${video.id}/share`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      } catch (expressError) {\n        // Direct to Laragon PHP API\n        response = await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${video.id}`, {\n          method: 'POST'\n        });\n        data = await response.json();\n      }\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares;\n      }\n    } catch (error) {\n      console.error('Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16', // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    }}>\n      {/* YouTube Video Iframe */}\n      <iframe\n        ref={iframeRef}\n        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer',\n          objectFit: 'cover' // Maintain aspect ratio\n        }}\n        onClick={handleVideoClick}\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        allowFullScreen\n        title={video.title}\n      />\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('API Response:', data);\n\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data\n            .filter(video => video.status === 'published') // Only published videos\n            .map(video => {\n              const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n              return {\n                id: video.id,\n                videoUrl: video.youtube_url,\n                youtubeId: youtubeId,\n                title: video.title || 'Untitled Video',\n                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',\n                tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],\n                author: {\n                  name: video.created_by || 'News Reporter',\n                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                  verified: true\n                },\n                stats: {\n                  likes: parseInt(video.likes) || 0,\n                  comments: parseInt(video.comments_count) || 0,\n                  shares: parseInt(video.shares) || 0,\n                  views: parseInt(video.views) || 0\n                },\n                uploadDate: video.created_at || new Date().toISOString(),\n                duration: video.duration || '03:00',\n                category: video.category || 'Video',\n                thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n              };\n            });\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n\n    const handleScroll = (e) => {\n      if (isScrolling) return;\n      isScrolling = true;\n\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n\n      setTimeout(() => { isScrolling = false; }, 300);\n    };\n\n    const handleTouchStart = (e) => {\n      startY = e.touches[0].clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n\n      if (Math.abs(diffY) > 50) { // Minimum swipe distance\n        isScrolling = true;\n\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n\n        setTimeout(() => { isScrolling = false; }, 300);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    }}>\n      <Box sx={{\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      }}>\n        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />\n      </Box>\n\n      <Typography variant=\"h6\" sx={{ mb: 1 }}>\n        Belum Ada Video\n      </Typography>\n\n      <Typography variant=\"body2\" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>\n        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\n      </Typography>\n\n      {/* Refresh Button */}\n      <IconButton\n        onClick={reloadVideos}\n        sx={{\n          color: 'white',\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },\n          mb: 3\n        }}\n      >\n        <i className=\"fas fa-refresh\" style={{ fontSize: 20 }} />\n      </IconButton>\n\n      <Typography variant=\"caption\" sx={{ opacity: 0.5 }}>\n        Ketuk untuk memuat ulang\n      </Typography>\n\n      {/* Placeholder UI Elements */}\n      <Box sx={{\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      }}>\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <FavoriteBorderIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos.length > 0 ? (\n        <>\n          {videos.map((video, index) => (\n            <Box\n              key={video.id}\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                transition: 'transform 0.3s ease-in-out'\n              }}\n            >\n              <VideoItem\n                video={video}\n                isActive={index === currentVideoIndex}\n              />\n            </Box>\n          ))}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACApC,SAAS,CAAC,MAAM;IACd,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4FAA4F,CAAC;QAC1H,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5Cd,UAAU,CAACc,IAAI,CAACA,IAAI,CAACK,QAAQ,CAACxB,KAAK,CAACyB,EAAE,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDV,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChB,KAAK,CAACyB,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMG,WAAW,GAAGf,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMgB,UAAU,GAAGhB,QAAQ,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMiB,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCF,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BG,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEP,OAAO;MACjBQ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MACPC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,iCAAiCb,OAAO,IAAIE,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACAnB,OAAO,CAACoB,GAAG,CAAC,gBAAgB,EAAE/C,KAAK,CAACgD,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,IAAI;QACF,MAAM9B,KAAK,CAAC,oCAAoClB,KAAK,CAACyB,EAAE,OAAO,EAAE;UAC/DwB,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB;QACA,MAAMhC,KAAK,CAAC,gGAAgGlB,KAAK,CAACyB,EAAE,EAAE,EAAE;UACtHwB,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGhD,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAAC4C,IAAI,IAAIjD,OAAO,GAAGiD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACF;MACA,IAAIpC,QAAQ;MACZ,IAAIE,IAAI;MAER,IAAI;QACFF,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoClB,KAAK,CAACyB,EAAE,OAAO,EAAE;UAC1EwB,MAAM,EAAE;QACV,CAAC,CAAC;QACF9B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAO8B,YAAY,EAAE;QACrB;QACAjC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4FAA4FlB,KAAK,CAACyB,EAAE,EAAE,EAAE;UAC7HwB,MAAM,EAAE;QACV,CAAC,CAAC;QACF9B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACAhB,UAAU,CAACc,IAAI,CAACmC,KAAK,CAAC;QACtB7C,QAAQ,CAACU,IAAI,CAACX,KAAK,CAAC;MACtB,CAAC,MAAM;QACL;QACAH,UAAU,CAAC+C,QAAQ,CAAC;QACpB3C,QAAQ,CAAC4C,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACArB,UAAU,CAAC+C,QAAQ,CAAC;MACpB3C,QAAQ,CAAC4C,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAME,UAAU,GAAGA,CAAA,KAAM;IACvBhD,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAMkD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9B7B,OAAO,CAACoB,GAAG,CAAC,cAAc,EAAE/C,KAAK,CAACyB,EAAE,CAAC;IAErC,IAAI;MACF;MACA,IAAIR,QAAQ;MACZ,IAAIE,IAAI;MAER,IAAI;QACFF,QAAQ,GAAG,MAAMC,KAAK,CAAC,oCAAoClB,KAAK,CAACyB,EAAE,QAAQ,EAAE;UAC3EwB,MAAM,EAAE;QACV,CAAC,CAAC;QACF9B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAC9B,CAAC,CAAC,OAAO8B,YAAY,EAAE;QACrB;QACAjC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gGAAgGlB,KAAK,CAACyB,EAAE,EAAE,EAAE;UACjIwB,MAAM,EAAE;QACV,CAAC,CAAC;QACF9B,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAC9B;MAEA,IAAID,IAAI,CAACE,OAAO,EAAE;QAChB;QACArB,KAAK,CAACU,KAAK,CAAC+C,MAAM,GAAGtC,IAAI,CAACsC,MAAM;MAClC;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACvD;EACF,CAAC;EAED,MAAMgC,aAAa,GAAGA,CAAA,KAAM;IAC1B/B,OAAO,CAACoB,GAAG,CAAC,0BAA0B,EAAE/C,KAAK,CAACyB,EAAE,CAAC;EACnD,CAAC;EAED,MAAMkC,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACEvE,OAAA,CAAChB,GAAG;IAACyF,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE3C,WAAW;MACnB4C,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAE5C,UAAU;MACpB6C,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,MAAM;MAAE;MACrBC,YAAY,EAAEpE,QAAQ,GAAG,CAAC,GAAG;IAC/B,CAAE;IAAAqE,QAAA,gBAEAtF,OAAA;MACEuF,GAAG,EAAExE,SAAU;MACfyE,GAAG,EAAEtD,kBAAkB,CAAC9B,KAAK,CAACqF,SAAS,EAAEpF,QAAQ,CAAE;MACnDqF,KAAK,EAAE;QACLd,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdgB,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,OAAO,CAAC;MACrB,CAAE;MACFC,OAAO,EAAE5C,gBAAiB;MAC1B6C,KAAK,EAAC,0FAA0F;MAChGC,eAAe;MACf5C,KAAK,EAAEhD,KAAK,CAACgD;IAAM;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFpG,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2B,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTb,MAAM,EAAE;MACV,CAAE;MACFE,OAAO,EAAE5C;IAAiB;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFpG,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB6B,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXxB,OAAO,EAAE,MAAM;QACf0B,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNF,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAtF,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACb,MAAM;UACLqG,GAAG,EAAEpF,KAAK,CAACwG,MAAM,CAACC,MAAO;UACzBpC,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVgB,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACDhG,KAAK,CAACwG,MAAM,CAACE,QAAQ,iBACpB9G,OAAA,CAAChB,GAAG;UAACyF,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClBM,YAAY,EAAE,KAAK;YACnBT,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB6B,EAAE,EAAE,CAAC,CAAC;YACNpB,MAAM,EAAE;UACV,CAAE;UAAAL,QAAA,eACAtF,OAAA,CAACf,UAAU;YAACwF,EAAE,EAAE;cAAEuC,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA5B,QAAA,EAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACT4G,OAAO,EAAEvC,UAAW;UACpBkB,EAAE,EAAE;YACFuC,KAAK,EAAExG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCuE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAED9E,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAA0G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGpG,OAAA,CAACR,kBAAkB;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD1E,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEuG,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGvG;QAAK;UAAAqF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACT4G,OAAO,EAAEhC,aAAc;UACvBW,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFtF,OAAA,CAACP,qBAAqB;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvDlF,KAAK,CAACU,KAAK,CAACsG;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACT4G,OAAO,EAAEnC,UAAW;UACpBc,EAAE,EAAE;YACFuC,KAAK,EAAEtG,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCqE,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAED5E,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGpG,OAAA,CAACN,kBAAkB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAE3D;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACT4G,OAAO,EAAElC,WAAY;UACrBa,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFtF,OAAA,CAACJ,SAAS;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvDlF,KAAK,CAACU,KAAK,CAAC+C;QAAM;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpG,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB8B,MAAM,EAAE,EAAE;QACVF,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTE,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAtF,OAAA,CAACf,UAAU;QAACwF,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE;QACN,CAAE;QAAA/B,QAAA,GACCvB,UAAU,CAAC3D,KAAK,CAACkH,UAAU,CAAC,EAAC,UAAG,EAAClH,KAAK,CAACmH,QAAQ;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbpG,OAAA,CAACf,UAAU;QAACwF,EAAE,EAAE;UACduC,KAAK,EAAE,OAAO;UACdE,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE;QACd,CAAE;QAAAlC,QAAA,EACClF,KAAK,CAACgD;MAAK;QAAA6C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbpG,OAAA,CAACf,UAAU;QAACwF,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE,GAAG;UACfxC,OAAO,EAAE,aAAa;UACtByC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BvC,QAAQ,EAAE;QACZ,CAAE;QAAAG,QAAA,EACClF,KAAK,CAACuH;MAAW;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE2B,GAAG,EAAE,CAAC;UAAEiB,QAAQ,EAAE;QAAO,CAAE;QAAAtC,QAAA,EACpDlF,KAAK,CAACyH,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzBhI,OAAA,CAACZ,IAAI;UAEH6I,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZzD,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnCiC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZtC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGiD,KAAK;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAA7F,EAAA,CAzXSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAA6I,EAAA,GANvBhI,SAAS;AA0XlB,eAAe,SAASiI,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1J,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAAC2J,MAAM,EAAEC,SAAS,CAAC,GAAG5J,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAAC6J,OAAO,EAAEC,UAAU,CAAC,GAAG9J,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,MAAM6J,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF5G,OAAO,CAACoB,GAAG,CAAC,iCAAiC,CAAC;;QAE9C;QACA,MAAM9B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gFAAgF,CAAC;QAE9G,IAAI,CAACD,QAAQ,CAACwH,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBzH,QAAQ,CAAC0H,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMxH,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCO,OAAO,CAACoB,GAAG,CAAC,eAAe,EAAE5B,IAAI,CAAC;QAElC,IAAIA,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAMyH,QAAQ,GAAGzH,IAAI,CAACA,IAAI,CACvB0H,MAAM,CAAC7I,KAAK,IAAIA,KAAK,CAAC2I,MAAM,KAAK,WAAW,CAAC,CAAC;UAAA,CAC9CjB,GAAG,CAAC1H,KAAK,IAAI;YAAA,IAAA8I,cAAA,EAAAC,eAAA;YACZ,MAAM1D,SAAS,GAAGrF,KAAK,CAACgJ,UAAU,IAAIC,gBAAgB,CAACjJ,KAAK,CAACkJ,WAAW,CAAC;YACzE,OAAO;cACLzH,EAAE,EAAEzB,KAAK,CAACyB,EAAE;cACZ0H,QAAQ,EAAEnJ,KAAK,CAACkJ,WAAW;cAC3B7D,SAAS,EAAEA,SAAS;cACpBrC,KAAK,EAAEhD,KAAK,CAACgD,KAAK,IAAI,gBAAgB;cACtCuE,WAAW,EAAEvH,KAAK,CAACuH,WAAW,IAAI,EAAAuB,cAAA,GAAA9I,KAAK,CAACoJ,OAAO,cAAAN,cAAA,uBAAbA,cAAA,CAAeO,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK,IAAI,0BAA0B;cACxG5B,IAAI,EAAEzH,KAAK,CAACyH,IAAI,GAAGzH,KAAK,CAACyH,IAAI,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC5B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC4B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAAR,eAAA,GAAA/I,KAAK,CAACwJ,QAAQ,cAAAT,eAAA,uBAAdA,eAAA,CAAgBU,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cAC5GjD,MAAM,EAAE;gBACNkD,IAAI,EAAE1J,KAAK,CAAC2J,UAAU,IAAI,eAAe;gBACzClD,MAAM,EAAE,gCAAgC,IAAIzG,KAAK,CAACyB,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9DiF,QAAQ,EAAE;cACZ,CAAC;cACDhG,KAAK,EAAE;gBACLF,KAAK,EAAEoJ,QAAQ,CAAC5J,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCwG,QAAQ,EAAE4C,QAAQ,CAAC5J,KAAK,CAAC6J,cAAc,CAAC,IAAI,CAAC;gBAC7CpG,MAAM,EAAEmG,QAAQ,CAAC5J,KAAK,CAACyD,MAAM,CAAC,IAAI,CAAC;gBACnCqG,KAAK,EAAEF,QAAQ,CAAC5J,KAAK,CAAC8J,KAAK,CAAC,IAAI;cAClC,CAAC;cACD5C,UAAU,EAAElH,KAAK,CAAC+J,UAAU,IAAI,IAAIjG,IAAI,CAAC,CAAC,CAACkG,WAAW,CAAC,CAAC;cACxD7C,QAAQ,EAAEnH,KAAK,CAACmH,QAAQ,IAAI,OAAO;cACnCqC,QAAQ,EAAExJ,KAAK,CAACwJ,QAAQ,IAAI,OAAO;cACnCS,SAAS,EAAE,8BAA8B5E,SAAS;YACpD,CAAC;UACH,CAAC,CAAC;UAEJ1D,OAAO,CAACoB,GAAG,CAAC,mBAAmB,EAAE6F,QAAQ,CAAC;;UAE1C;UACA,IAAIA,QAAQ,CAACsB,MAAM,GAAG,CAAC,EAAE;YACvB7B,SAAS,CAACO,QAAQ,CAAC;YACnBjH,OAAO,CAACoB,GAAG,CAAC,gCAAgC,EAAE6F,QAAQ,CAACsB,MAAM,CAAC;UAChE,CAAC,MAAM;YACLvI,OAAO,CAACoB,GAAG,CAAC,mDAAmD,CAAC;YAChEsF,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACL1G,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEP,IAAI,CAAC;UACnDkH,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAO3G,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACA2G,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,gBAAgB,GAAIkB,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAMC,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAGF,GAAG,CAACE,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACH,MAAM,KAAK,EAAE,GAAIG,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACA1L,SAAS,CAAC,MAAM;IACd,IAAIyJ,MAAM,CAAC8B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,IAAII,MAAM,GAAG,CAAC;IACd,IAAIC,WAAW,GAAG,KAAK;IAEvB,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIF,WAAW,EAAE;MACjBA,WAAW,GAAG,IAAI;MAElB,IAAIE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIxC,iBAAiB,GAAGE,MAAM,CAAC8B,MAAM,GAAG,CAAC,EAAE;QACzD/B,oBAAoB,CAAC9E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAIoH,CAAC,CAACC,MAAM,GAAG,CAAC,IAAIxC,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAAC9E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;MAEAsH,UAAU,CAAC,MAAM;QAAEJ,WAAW,GAAG,KAAK;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC;IAED,MAAMK,gBAAgB,GAAIH,CAAC,IAAK;MAC9BH,MAAM,GAAGG,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC/B,CAAC;IAED,MAAMC,cAAc,GAAIN,CAAC,IAAK;MAC5B,IAAIF,WAAW,EAAE;MACjB,MAAMS,IAAI,GAAGP,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;MACxC,MAAMI,KAAK,GAAGZ,MAAM,GAAGU,IAAI;MAE3B,IAAI/G,IAAI,CAACC,GAAG,CAACgH,KAAK,CAAC,GAAG,EAAE,EAAE;QAAE;QAC1BX,WAAW,GAAG,IAAI;QAElB,IAAIW,KAAK,GAAG,CAAC,IAAIhD,iBAAiB,GAAGE,MAAM,CAAC8B,MAAM,GAAG,CAAC,EAAE;UACtD;UACA/B,oBAAoB,CAAC9E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAI6H,KAAK,GAAG,CAAC,IAAIhD,iBAAiB,GAAG,CAAC,EAAE;UAC7C;UACAC,oBAAoB,CAAC9E,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC;QAEAsH,UAAU,CAAC,MAAM;UAAEJ,WAAW,GAAG,KAAK;QAAE,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC;IAEDY,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEZ,YAAY,CAAC;IAC9CW,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,CAAC;IACvDO,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEL,cAAc,CAAC;IAEnD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEb,YAAY,CAAC;MACjDW,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;MAC1DO,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC7C,iBAAiB,EAAEE,MAAM,CAAC8B,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMoB,YAAY,GAAGA,CAAA,KAAM;IACzB/C,UAAU,CAAC,IAAI,CAAC;IAChB;IACA4C,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtB7L,OAAA,CAAChB,GAAG;IAACyF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE,OAAO;MACd8E,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAE;IAAAzG,QAAA,gBACAtF,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBN,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBmC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eACAtF,OAAA,CAACH,aAAa;QAAC4E,EAAE,EAAE;UAAEwC,QAAQ,EAAE,EAAE;UAAE+E,OAAO,EAAE;QAAI;MAAE;QAAA/F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENpG,OAAA,CAACf,UAAU;MAACgN,OAAO,EAAC,IAAI;MAACxH,EAAE,EAAE;QAAE4C,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbpG,OAAA,CAACf,UAAU;MAACgN,OAAO,EAAC,OAAO;MAACxH,EAAE,EAAE;QAAEuH,OAAO,EAAE,GAAG;QAAEnH,QAAQ,EAAE,GAAG;QAAEwC,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExE;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbpG,OAAA,CAACd,UAAU;MACT4G,OAAO,EAAE4F,YAAa;MACtBjH,EAAE,EAAE;QACFuC,KAAK,EAAE,OAAO;QACdjC,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE;UAAEA,OAAO,EAAE;QAA2B,CAAC;QAClDsC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eAEFtF,OAAA;QAAGkM,SAAS,EAAC,gBAAgB;QAACxG,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEbpG,OAAA,CAACf,UAAU;MAACgN,OAAO,EAAC,SAAS;MAACxH,EAAE,EAAE;QAAEuH,OAAO,EAAE;MAAI,CAAE;MAAA1G,QAAA,EAAC;IAEpD;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbpG,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPO,OAAO,EAAE,MAAM;QACf2B,GAAG,EAAE,CAAC;QACNI,EAAE,EAAE;MACN,CAAE;MAAAzB,QAAA,gBAEAtF,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACTiN,QAAQ;UACR1H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEiH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA1G,QAAA,eAEFtF,OAAA,CAACR,kBAAkB;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEiF,OAAO,EAAE;UAAI,CAAE;UAAA1G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACTiN,QAAQ;UACR1H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEiH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA1G,QAAA,eAEFtF,OAAA,CAACP,qBAAqB;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEiF,OAAO,EAAE;UAAI,CAAE;UAAA1G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1EtF,OAAA,CAACd,UAAU;UACTiN,QAAQ;UACR1H,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAEiH,OAAO,EAAE;YAAI;UACnC,CAAE;UAAA1G,QAAA,eAEFtF,OAAA,CAACJ,SAAS;YAAAqG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbpG,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAEiF,OAAO,EAAE;UAAI,CAAE;UAAA1G,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAMgG,iBAAiB,GAAGA,CAAA,kBACxBpM,OAAA,CAAChB,GAAG;IAACyF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE;IACT,CAAE;IAAA1B,QAAA,gBACAtF,OAAA,CAAChB,GAAG;MAACyF,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBM,MAAM,EAAE,iCAAiC;QACzC0G,SAAS,EAAE,iBAAiB;QAC5BC,SAAS,EAAE,yBAAyB;QACpCjF,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAEkF,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAAtG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAELpG,OAAA,CAACf,UAAU;MAACgN,OAAO,EAAC,OAAO;MAAA3G,QAAA,EAAC;IAE5B;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAED,oBACEpG,OAAA,CAAChB,GAAG;IAACyF,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE,UAAU;MACpBK,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAI,QAAA,EACCoD,OAAO,gBACN1I,OAAA,CAACoM,iBAAiB;MAAAnG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBoC,MAAM,CAAC8B,MAAM,GAAG,CAAC,gBACnBtK,OAAA,CAAAE,SAAA;MAAAoF,QAAA,GACGkD,MAAM,CAACV,GAAG,CAAC,CAAC1H,KAAK,EAAE4H,KAAK,kBACvBhI,OAAA,CAAChB,GAAG;QAEFyF,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACP1B,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBqH,SAAS,EAAE,cAAc,CAACvE,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;UAC9DkE,UAAU,EAAE;QACd,CAAE;QAAAlH,QAAA,eAEFtF,OAAA,CAACG,SAAS;UACRC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAE2H,KAAK,KAAKM;QAAkB;UAAArC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC,GAjBGhG,KAAK,CAACyB,EAAE;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBV,CACN,CAAC,eAGFpG,OAAA,CAAChB,GAAG;QAACyF,EAAE,EAAE;UACPC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,EAAE;UACPE,KAAK,EAAE,EAAE;UACTxB,OAAO,EAAE,oBAAoB;UAC7BM,YAAY,EAAE,CAAC;UACfoH,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLjG,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,eACAtF,OAAA,CAACf,UAAU;UAACwF,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA3B,QAAA,GAC9CgD,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAAC8B,MAAM;QAAA;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEHpG,OAAA,CAAC6L,eAAe;MAAA5F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACiC,GAAA,CA7VuBD,SAAS;AAAAuE,GAAA,GAATvE,SAAS;AAAA,IAAAD,EAAA,EAAAwE,GAAA;AAAAC,YAAA,CAAAzE,EAAA;AAAAyE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}