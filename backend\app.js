// Entry point for backend Express app
require('dotenv').config();
const express = require('express');
const app = express();
const cors = require('cors');
const path = require('path');

// Configure CORS properly
app.use(cors({
  origin: [
    'http://localhost:3000', 'http://localhost:3001', 'http://localhost:5173',
    'http://127.0.0.1:3000', 'http://127.0.0.1:3001', 'http://127.0.0.1:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// Middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Debug middleware (only in development)
if (process.env.NODE_ENV !== 'production') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
    next();
  });
}

// Serve static files
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));
app.use('/uploads/setting', express.static(path.join(__dirname, 'uploads/setting')));

// Serve admin PHP files and assets
app.use('/admin/php', express.static(path.join(__dirname, '../frontend/src/pages/admin')));
app.use('/admin/assets', express.static(path.join(__dirname, '../frontend/src/pages/admin/assets')));

// Import controllers safely
let postController;
try {
  postController = require('./controller/postController');
} catch (error) {
  console.log('PostController not found, using fallback...');
  postController = {
    getVideos: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    getVideoById: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    toggleVideoLike: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    incrementVideoShare: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    incrementVideoView: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    getPosts: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    getPostById: (req, res) => res.json({ success: false, message: 'Controller not found' }),
    incrementShare: (req, res) => res.json({ success: false, message: 'Controller not found' })
  };
}

// Admin dashboard routes - redirect to localhost PHP
app.get('/admin/dashboard', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=dashboard');
});

app.get('/admin/dashboard/:page', (req, res) => {
  const page = req.params.page;
  res.redirect(`http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=${page}`);
});

// Clean URL routes for admin pages
app.get('/admin/videos', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=videos');
});

app.get('/admin/add-video', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=add-video');
});

app.get('/admin/news', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=news');
});

app.get('/admin/add-news', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=add-news');
});

app.get('/admin/settings', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/dashboard.php?page=settings');
});

// API routes - redirect to localhost PHP
app.all('/admin/api/:action', (req, res) => {
  const action = req.params.action;
  const id = req.query.id || '';
  const idParam = id ? `&id=${id}` : '';
  res.redirect(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=${action}${idParam}`);
});

// Video API routes
app.get('/api/videos', (req, res) => {
  res.redirect('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');
});

app.get('/api/videos/:id', (req, res) => {
  const id = req.params.id;
  res.redirect(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video&id=${id}`);
});

app.post('/api/videos/:id/like', (req, res) => {
  const id = req.params.id;
  res.redirect(307, `http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like&id=${id}`);
});

app.post('/api/videos/:id/view', (req, res) => {
  const id = req.params.id;
  res.redirect(307, `http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${id}`);
});

app.post('/api/videos/:id/share', (req, res) => {
  const id = req.params.id;
  res.redirect(307, `http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share&id=${id}`);
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend server is running!',
    timestamp: new Date().toISOString(),
    port: process.env.PORT || 3000
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.originalUrl
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Server Error:', error);
  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`🚀 Backend server running on port ${PORT}`);
  console.log(`📊 Admin Dashboard: http://localhost:${PORT}/admin/dashboard`);
  console.log(`📹 Admin Videos: http://localhost:${PORT}/admin/videos`);
  console.log(`📰 Admin News: http://localhost:${PORT}/admin/news`);
  console.log(`⚙️  Admin Settings: http://localhost:${PORT}/admin/settings`);
  console.log(`🔗 API Health: http://localhost:${PORT}/health`);
  console.log(`📱 API Videos: http://localhost:${PORT}/api/videos`);
});
