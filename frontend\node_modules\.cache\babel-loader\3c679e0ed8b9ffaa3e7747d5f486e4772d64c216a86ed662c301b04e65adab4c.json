{"ast": null, "code": "var _jsxFileName = \"C:\\\\laragon\\\\www\\\\react-news\\\\frontend\\\\src\\\\components\\\\VideoFeed.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, IconButton, Avatar, Chip, useTheme, useMediaQuery } from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction VideoItem({\n  video,\n  isActive,\n  onVideoClick\n}) {\n  _s();\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n    loadLikedStatus();\n  }, [video.id]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded'\n        },\n        body: `id=${video.id}`\n      });\n      const data = await response.json();\n      console.log('Share response:', data);\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n  const formatDate = dateString => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16',\n      // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"iframe\", {\n      ref: iframeRef,\n      src: getYouTubeEmbedUrl(video.youtubeId, isActive),\n      style: {\n        width: '100%',\n        height: '100%',\n        border: 'none',\n        cursor: 'pointer',\n        objectFit: 'cover' // Maintain aspect ratio\n      },\n      onClick: handleVideoClick,\n      allow: \"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\",\n      allowFullScreen: true,\n      title: video.title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 210,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      },\n      onClick: handleVideoClick\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 227,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          src: video.author.avatar,\n          sx: {\n            width: 48,\n            height: 48,\n            border: '2px solid white',\n            cursor: 'pointer'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this), video.author.verified && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            bgcolor: '#1976d2',\n            borderRadius: '50%',\n            width: 20,\n            height: 20,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            mt: -1,\n            border: '2px solid white'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            sx: {\n              color: 'white',\n              fontSize: 10,\n              fontWeight: 'bold'\n            },\n            children: \"\\u2713\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleLike,\n          sx: {\n            color: isLiked ? '#e91e63' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isLiked ? /*#__PURE__*/_jsxDEV(FavoriteIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 287,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleComment,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.comments\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleSave,\n          sx: {\n            color: isSaved ? '#ffc107' : 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: isSaved ? /*#__PURE__*/_jsxDEV(BookmarkIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 24\n          }, this) : /*#__PURE__*/_jsxDEV(BookmarkBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 43\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: \"Simpan\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: handleShare,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(0, 0, 0, 0.3)',\n            '&:hover': {\n              bgcolor: 'rgba(0, 0, 0, 0.5)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5\n          },\n          children: video.stats.shares\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.8)',\n          fontSize: 12,\n          mb: 1\n        },\n        children: [formatDate(video.uploadDate), \" \\u2022 \", video.duration]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'white',\n          fontWeight: 'bold',\n          fontSize: 16,\n          mb: 1,\n          lineHeight: 1.3\n        },\n        children: video.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        sx: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: 14,\n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        },\n        children: video.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          flexWrap: 'wrap'\n        },\n        children: video.tags.map((tag, index) => /*#__PURE__*/_jsxDEV(Chip, {\n          label: `#${tag}`,\n          size: \"small\",\n          sx: {\n            bgcolor: 'rgba(255, 255, 255, 0.2)',\n            color: 'white',\n            fontSize: 11,\n            height: 24,\n            '&:hover': {\n              bgcolor: 'rgba(255, 255, 255, 0.3)'\n            }\n          }\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 389,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 347,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n}\n\n// Main VideoFeed Component\n_s(VideoItem, \"VfDT3SvRyjPCeKKMgd+ZxiIgKCQ=\", false, function () {\n  return [useTheme, useMediaQuery];\n});\n_c = VideoItem;\nexport default function VideoFeed() {\n  _s2();\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('API Response:', data);\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data.filter(video => video.status === 'published') // Only published videos\n          .map(video => {\n            var _video$content, _video$category;\n            const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n            return {\n              id: video.id,\n              videoUrl: video.youtube_url,\n              youtubeId: youtubeId,\n              title: video.title || 'Untitled Video',\n              description: video.description || ((_video$content = video.content) === null || _video$content === void 0 ? void 0 : _video$content.substring(0, 200)) + '...' || 'No description available',\n              tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [((_video$category = video.category) === null || _video$category === void 0 ? void 0 : _video$category.toLowerCase()) || 'video'],\n              author: {\n                name: video.created_by || 'News Reporter',\n                avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                verified: true\n              },\n              stats: {\n                likes: parseInt(video.likes) || 0,\n                comments: parseInt(video.comments_count) || 0,\n                shares: parseInt(video.shares) || 0,\n                views: parseInt(video.views) || 0\n              },\n              uploadDate: video.created_at || new Date().toISOString(),\n              duration: video.duration || '03:00',\n              category: video.category || 'Video',\n              thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n            };\n          });\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = url => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return match && match[2].length === 11 ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n    const handleScroll = e => {\n      if (isScrolling) return;\n      isScrolling = true;\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n      setTimeout(() => {\n        isScrolling = false;\n      }, 300);\n    };\n    const handleTouchStart = e => {\n      startY = e.touches[0].clientY;\n    };\n    const handleTouchEnd = e => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n      if (Math.abs(diffY) > 50) {\n        // Minimum swipe distance\n        isScrolling = true;\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n        setTimeout(() => {\n          isScrolling = false;\n        }, 300);\n      }\n    };\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(PlayArrowIcon, {\n        sx: {\n          fontSize: 40,\n          opacity: 0.7\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 583,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 573,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h6\",\n      sx: {\n        mb: 1\n      },\n      children: \"Belum Ada Video\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 586,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        opacity: 0.7,\n        maxWidth: 300,\n        mb: 4\n      },\n      children: \"Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 590,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n      onClick: reloadVideos,\n      sx: {\n        color: 'white',\n        bgcolor: 'rgba(255, 255, 255, 0.1)',\n        '&:hover': {\n          bgcolor: 'rgba(255, 255, 255, 0.2)'\n        },\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-refresh\",\n        style: {\n          fontSize: 20\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"caption\",\n      sx: {\n        opacity: 0.5\n      },\n      children: \"Ketuk untuk memuat ulang\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 607,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(FavoriteBorderIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 627,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 619,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ChatBubbleOutlineIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          disabled: true,\n          sx: {\n            color: 'white',\n            bgcolor: 'rgba(255, 255, 255, 0.1)',\n            '&.Mui-disabled': {\n              opacity: 0.5\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(ShareIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 661,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 653,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12,\n            mt: 0.5,\n            opacity: 0.7\n          },\n          children: \"0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 652,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 612,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 562,\n    columnNumber: 5\n  }, this);\n\n  // Loading state\n  const LoadingVideoState = () => /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': {\n            transform: 'rotate(0deg)'\n          },\n          '100%': {\n            transform: 'rotate(360deg)'\n          }\n        }\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      children: \"Memuat Video...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 696,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 673,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    },\n    children: loading ? /*#__PURE__*/_jsxDEV(LoadingVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 713,\n      columnNumber: 9\n    }, this) : videos.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [videos.map((video, index) => /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n          transition: 'transform 0.3s ease-in-out'\n        },\n        children: /*#__PURE__*/_jsxDEV(VideoItem, {\n          video: video,\n          isActive: index === currentVideoIndex\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 732,\n          columnNumber: 15\n        }, this)\n      }, video.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 717,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          position: 'absolute',\n          top: 20,\n          right: 20,\n          bgcolor: 'rgba(0, 0, 0, 0.5)',\n          borderRadius: 2,\n          px: 2,\n          py: 1,\n          zIndex: 4\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          sx: {\n            color: 'white',\n            fontSize: 12\n          },\n          children: [currentVideoIndex + 1, \" / \", videos.length]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 750,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 740,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true) : /*#__PURE__*/_jsxDEV(EmptyVideoState, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 756,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 703,\n    columnNumber: 5\n  }, this);\n}\n_s2(VideoFeed, \"C87+OgytXDlcze02hYwWuiUEmSU=\");\n_c2 = VideoFeed;\nvar _c, _c2;\n$RefreshReg$(_c, \"VideoItem\");\n$RefreshReg$(_c2, \"VideoFeed\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "IconButton", "Avatar", "Chip", "useTheme", "useMediaQuery", "FavoriteIcon", "FavoriteBorderIcon", "ChatBubbleOutlineIcon", "BookmarkBorderIcon", "BookmarkIcon", "ShareIcon", "PlayArrowIcon", "PauseIcon", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "VideoItem", "video", "isActive", "onVideoClick", "_s", "isLiked", "setIsLiked", "isSaved", "setIsSaved", "likes", "setLikes", "stats", "iframeRef", "theme", "isMobile", "breakpoints", "down", "loadLikedStatus", "response", "fetch", "data", "json", "success", "Array", "isArray", "includes", "id", "error", "console", "videoHeight", "videoWidth", "getYouTubeEmbedUrl", "videoId", "autoplay", "params", "URLSearchParams", "mute", "controls", "loop", "playlist", "rel", "showinfo", "modestbranding", "iv_load_policy", "fs", "disablekb", "toString", "handleVideoClick", "log", "title", "method", "expressError", "handleLike", "wasLiked", "prev", "headers", "body", "liked", "message", "handleSave", "handleShare", "shares", "navigator", "share", "text", "description", "url", "videoUrl", "clipboard", "writeText", "alert", "handleComment", "formatDate", "dateString", "date", "Date", "now", "diffTime", "Math", "abs", "diffDays", "ceil", "sx", "position", "height", "width", "max<PERSON><PERSON><PERSON>", "mx", "bgcolor", "display", "alignItems", "justifyContent", "overflow", "aspectRatio", "borderRadius", "children", "ref", "src", "youtubeId", "style", "border", "cursor", "objectFit", "onClick", "allow", "allowFullScreen", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "top", "left", "right", "bottom", "zIndex", "flexDirection", "gap", "author", "avatar", "verified", "mt", "color", "fontSize", "fontWeight", "toFixed", "comments", "mb", "uploadDate", "duration", "lineHeight", "WebkitLineClamp", "WebkitBoxOrient", "flexWrap", "tags", "map", "tag", "index", "label", "size", "_c", "VideoFeed", "_s2", "currentVideoIndex", "setCurrentVideoIndex", "videos", "setVideos", "loading", "setLoading", "loadVideos", "ok", "Error", "status", "dbVideos", "filter", "_video$content", "_video$category", "youtube_id", "extractYouTubeId", "youtube_url", "content", "substring", "split", "trim", "category", "toLowerCase", "name", "created_by", "parseInt", "comments_count", "views", "created_at", "toISOString", "thumbnail", "length", "regExp", "match", "startY", "isScrolling", "handleScroll", "e", "deltaY", "setTimeout", "handleTouchStart", "touches", "clientY", "handleTouchEnd", "endY", "changedTouches", "diffY", "window", "addEventListener", "removeEventListener", "reloadVideos", "location", "reload", "EmptyVideoState", "padding", "textAlign", "opacity", "variant", "className", "disabled", "LoadingVideoState", "borderTop", "animation", "transform", "transition", "px", "py", "_c2", "$RefreshReg$"], "sources": ["C:/laragon/www/react-news/frontend/src/components/VideoFeed.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  Typography,\n  IconButton,\n  Avatar,\n  Chip,\n  useTheme,\n  useMediaQuery\n} from '@mui/material';\nimport FavoriteIcon from '@mui/icons-material/Favorite';\nimport FavoriteBorderIcon from '@mui/icons-material/FavoriteBorder';\nimport ChatBubbleOutlineIcon from '@mui/icons-material/ChatBubbleOutline';\nimport BookmarkBorderIcon from '@mui/icons-material/BookmarkBorder';\nimport BookmarkIcon from '@mui/icons-material/Bookmark';\nimport ShareIcon from '@mui/icons-material/Share';\nimport PlayArrowIcon from '@mui/icons-material/PlayArrow';\nimport PauseIcon from '@mui/icons-material/Pause';\n\n// Video data will be loaded from database via API\n\n// Individual Video Item Component\nfunction VideoItem({ video, isActive, onVideoClick }) {\n  const [isLiked, setIsLiked] = useState(false);\n  const [isSaved, setIsSaved] = useState(false);\n  const [likes, setLikes] = useState(video.stats.likes);\n  const iframeRef = useRef(null);\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('md'));\n\n  // Load liked status from database\n  useEffect(() => {\n    const loadLikedStatus = async () => {\n      try {\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_video_liked_status');\n        const data = await response.json();\n\n        if (data.success && Array.isArray(data.data)) {\n          setIsLiked(data.data.includes(video.id));\n        }\n      } catch (error) {\n        console.error('Failed to load liked status:', error);\n      }\n    };\n\n    loadLikedStatus();\n  }, [video.id]);\n\n  // TikTok aspect ratio: 9:16 (vertical) - Always vertical\n  const videoHeight = isMobile ? '100vh' : '85vh';\n  const videoWidth = isMobile ? '100vw' : `${85 * 9 / 16}vh`; // Perfect 9:16 ratio\n\n  // YouTube iframe API for better control\n  const getYouTubeEmbedUrl = (videoId, autoplay = false) => {\n    const params = new URLSearchParams({\n      autoplay: autoplay ? '1' : '0',\n      mute: '1',\n      controls: '0',\n      loop: '1',\n      playlist: videoId,\n      rel: '0',\n      showinfo: '0',\n      modestbranding: '1',\n      iv_load_policy: '3',\n      fs: '0',\n      disablekb: '1'\n    });\n    return `https://www.youtube.com/embed/${videoId}?${params.toString()}`;\n  };\n\n  const handleVideoClick = async () => {\n    // For YouTube iframe, we'll just show interaction feedback\n    console.log('Video clicked:', video.title);\n\n    try {\n      // Try Express API first, fallback to PHP API\n      try {\n        await fetch(`http://localhost:3000/api/videos/${video.id}/view`, {\n          method: 'POST'\n        });\n      } catch (expressError) {\n        // Direct to localhost PHP API\n        await fetch(`http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_views&id=${video.id}`, {\n          method: 'POST'\n        });\n      }\n    } catch (error) {\n      console.error('Failed to update view count:', error);\n    }\n  };\n\n  const handleLike = async () => {\n    const wasLiked = isLiked;\n\n    // Optimistic update\n    setIsLiked(!isLiked);\n    setLikes(prev => isLiked ? prev - 1 : prev + 1);\n\n    try {\n      console.log('Toggling like for video:', video.id);\n\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=toggle_video_like', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Like toggle response:', data);\n\n      if (data.success) {\n        // Update with server response\n        setIsLiked(data.liked || !wasLiked);\n        setLikes(data.likes || (wasLiked ? likes - 1 : likes + 1));\n        console.log('✅ Like toggled successfully');\n      } else {\n        console.error('❌ Failed to toggle like on server:', data.message);\n        // Revert on failure\n        setIsLiked(wasLiked);\n        setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n      }\n    } catch (error) {\n      console.error('❌ Failed to toggle video like:', error);\n      // Revert on error\n      setIsLiked(wasLiked);\n      setLikes(prev => wasLiked ? prev + 1 : prev - 1);\n    }\n  };\n\n  const handleSave = () => {\n    setIsSaved(!isSaved);\n  };\n\n  const handleShare = async () => {\n    console.log('Sharing video:', video.id);\n\n    try {\n      // Direct to Laragon PHP API (more reliable)\n      const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=increment_video_share', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n        },\n        body: `id=${video.id}`\n      });\n\n      const data = await response.json();\n      console.log('Share response:', data);\n\n      if (data.success) {\n        // Update local share count\n        video.stats.shares = data.shares || video.stats.shares + 1;\n        console.log('✅ Share count updated successfully');\n\n        // Show share options (you can customize this)\n        if (navigator.share) {\n          navigator.share({\n            title: video.title,\n            text: video.description,\n            url: video.videoUrl\n          });\n        } else {\n          // Fallback: copy to clipboard\n          navigator.clipboard.writeText(video.videoUrl);\n          alert('Link video telah disalin ke clipboard!');\n        }\n      } else {\n        console.error('❌ Failed to update share count:', data.message);\n      }\n    } catch (error) {\n      console.error('❌ Failed to update share count:', error);\n    }\n  };\n\n  const handleComment = () => {\n    console.log('Open comments for video:', video.id);\n  };\n\n  const formatDate = (dateString) => {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffTime = Math.abs(now - date);\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays === 1) return '1 hari lalu';\n    if (diffDays < 7) return `${diffDays} hari lalu`;\n    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} minggu lalu`;\n    return `${Math.ceil(diffDays / 30)} bulan lalu`;\n  };\n\n  return (\n    <Box sx={{\n      position: 'relative',\n      height: videoHeight,\n      width: '100%',\n      maxWidth: videoWidth,\n      mx: 'auto',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      overflow: 'hidden',\n      aspectRatio: '9/16', // Force 9:16 aspect ratio\n      borderRadius: isMobile ? 0 : 2\n    }}>\n      {/* YouTube Video Iframe */}\n      <iframe\n        ref={iframeRef}\n        src={getYouTubeEmbedUrl(video.youtubeId, isActive)}\n        style={{\n          width: '100%',\n          height: '100%',\n          border: 'none',\n          cursor: 'pointer',\n          objectFit: 'cover' // Maintain aspect ratio\n        }}\n        onClick={handleVideoClick}\n        allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture\"\n        allowFullScreen\n        title={video.title}\n      />\n\n      {/* Video Overlay for Interaction */}\n      <Box sx={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        zIndex: 1,\n        cursor: 'pointer'\n      }}\n      onClick={handleVideoClick}\n      />\n\n      {/* Right Side Actions (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        right: 12,\n        bottom: 100,\n        display: 'flex',\n        flexDirection: 'column',\n        gap: 3,\n        zIndex: 3\n      }}>\n        {/* Profile Avatar */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <Avatar\n            src={video.author.avatar}\n            sx={{ \n              width: 48, \n              height: 48, \n              border: '2px solid white',\n              cursor: 'pointer'\n            }}\n          />\n          {video.author.verified && (\n            <Box sx={{\n              bgcolor: '#1976d2',\n              borderRadius: '50%',\n              width: 20,\n              height: 20,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              mt: -1,\n              border: '2px solid white'\n            }}>\n              <Typography sx={{ color: 'white', fontSize: 10, fontWeight: 'bold' }}>✓</Typography>\n            </Box>\n          )}\n        </Box>\n\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleLike}\n            sx={{\n              color: isLiked ? '#e91e63' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isLiked ? <FavoriteIcon /> : <FavoriteBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {likes > 1000 ? `${(likes / 1000).toFixed(1)}k` : likes}\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleComment}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.comments}\n          </Typography>\n        </Box>\n\n        {/* Save Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleSave}\n            sx={{\n              color: isSaved ? '#ffc107' : 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            {isSaved ? <BookmarkIcon /> : <BookmarkBorderIcon />}\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            Simpan\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            onClick={handleShare}\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(0, 0, 0, 0.3)',\n              '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.5)' }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5 }}>\n            {video.stats.shares}\n          </Typography>\n        </Box>\n      </Box>\n\n      {/* Bottom Info (TikTok Style) */}\n      <Box sx={{\n        position: 'absolute',\n        bottom: 20,\n        left: 16,\n        right: 80,\n        zIndex: 3\n      }}>\n        {/* Upload Date and Duration */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.8)', \n          fontSize: 12, \n          mb: 1 \n        }}>\n          {formatDate(video.uploadDate)} • {video.duration}\n        </Typography>\n\n        {/* Title */}\n        <Typography sx={{ \n          color: 'white', \n          fontWeight: 'bold', \n          fontSize: 16, \n          mb: 1,\n          lineHeight: 1.3\n        }}>\n          {video.title}\n        </Typography>\n\n        {/* Description */}\n        <Typography sx={{ \n          color: 'rgba(255, 255, 255, 0.9)', \n          fontSize: 14, \n          mb: 2,\n          lineHeight: 1.4,\n          display: '-webkit-box',\n          WebkitLineClamp: 2,\n          WebkitBoxOrient: 'vertical',\n          overflow: 'hidden'\n        }}>\n          {video.description}\n        </Typography>\n\n        {/* Tags */}\n        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>\n          {video.tags.map((tag, index) => (\n            <Chip\n              key={index}\n              label={`#${tag}`}\n              size=\"small\"\n              sx={{\n                bgcolor: 'rgba(255, 255, 255, 0.2)',\n                color: 'white',\n                fontSize: 11,\n                height: 24,\n                '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.3)' }\n              }}\n            />\n          ))}\n        </Box>\n      </Box>\n    </Box>\n  );\n}\n\n// Main VideoFeed Component\nexport default function VideoFeed() {\n  const [currentVideoIndex, setCurrentVideoIndex] = useState(0);\n  const [videos, setVideos] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  // Load videos from admin database\n  useEffect(() => {\n    const loadVideos = async () => {\n      setLoading(true);\n      try {\n        console.log('Loading videos from database...');\n\n        // Direct to localhost PHP API (more reliable)\n        const response = await fetch('http://localhost/react-news/frontend/src/pages/admin/api.php?action=get_videos');\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('API Response:', data);\n\n        if (data.success && Array.isArray(data.data)) {\n          // Convert database video data to component format\n          const dbVideos = data.data\n            .filter(video => video.status === 'published') // Only published videos\n            .map(video => {\n              const youtubeId = video.youtube_id || extractYouTubeId(video.youtube_url);\n              return {\n                id: video.id,\n                videoUrl: video.youtube_url,\n                youtubeId: youtubeId,\n                title: video.title || 'Untitled Video',\n                description: video.description || video.content?.substring(0, 200) + '...' || 'No description available',\n                tags: video.tags ? video.tags.split(',').map(tag => tag.trim()) : [video.category?.toLowerCase() || 'video'],\n                author: {\n                  name: video.created_by || 'News Reporter',\n                  avatar: 'https://i.pravatar.cc/150?img=' + (video.id % 10 + 1),\n                  verified: true\n                },\n                stats: {\n                  likes: parseInt(video.likes) || 0,\n                  comments: parseInt(video.comments_count) || 0,\n                  shares: parseInt(video.shares) || 0,\n                  views: parseInt(video.views) || 0\n                },\n                uploadDate: video.created_at || new Date().toISOString(),\n                duration: video.duration || '03:00',\n                category: video.category || 'Video',\n                thumbnail: `https://img.youtube.com/vi/${youtubeId}/maxresdefault.jpg`\n              };\n            });\n\n          console.log('Processed videos:', dbVideos);\n\n          // Use database videos if available, otherwise show empty state\n          if (dbVideos.length > 0) {\n            setVideos(dbVideos);\n            console.log('✅ Loaded videos from database:', dbVideos.length);\n          } else {\n            console.log('⚠️ No published videos found, showing empty state');\n            setVideos([]);\n          }\n        } else {\n          console.error('❌ API returned invalid data:', data);\n          setVideos([]);\n        }\n      } catch (error) {\n        console.error('❌ Failed to load videos:', error);\n        // Show empty state on error\n        setVideos([]);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadVideos();\n  }, []);\n\n  // Extract YouTube video ID from URL\n  const extractYouTubeId = (url) => {\n    if (!url) return null;\n    const regExp = /^.*(youtu.be\\/|v\\/|u\\/\\w\\/|embed\\/|watch\\?v=|&v=)([^#&?]*).*/;\n    const match = url.match(regExp);\n    return (match && match[2].length === 11) ? match[2] : 'dQw4w9WgXcQ'; // Default video ID\n  };\n\n  // Handle scroll and touch to change videos (only if videos exist)\n  useEffect(() => {\n    if (videos.length === 0) return; // Don't add listeners if no videos\n\n    let startY = 0;\n    let isScrolling = false;\n\n    const handleScroll = (e) => {\n      if (isScrolling) return;\n      isScrolling = true;\n\n      if (e.deltaY > 0 && currentVideoIndex < videos.length - 1) {\n        setCurrentVideoIndex(prev => prev + 1);\n      } else if (e.deltaY < 0 && currentVideoIndex > 0) {\n        setCurrentVideoIndex(prev => prev - 1);\n      }\n\n      setTimeout(() => { isScrolling = false; }, 300);\n    };\n\n    const handleTouchStart = (e) => {\n      startY = e.touches[0].clientY;\n    };\n\n    const handleTouchEnd = (e) => {\n      if (isScrolling) return;\n      const endY = e.changedTouches[0].clientY;\n      const diffY = startY - endY;\n\n      if (Math.abs(diffY) > 50) { // Minimum swipe distance\n        isScrolling = true;\n\n        if (diffY > 0 && currentVideoIndex < videos.length - 1) {\n          // Swipe up - next video\n          setCurrentVideoIndex(prev => prev + 1);\n        } else if (diffY < 0 && currentVideoIndex > 0) {\n          // Swipe down - previous video\n          setCurrentVideoIndex(prev => prev - 1);\n        }\n\n        setTimeout(() => { isScrolling = false; }, 300);\n      }\n    };\n\n    window.addEventListener('wheel', handleScroll);\n    window.addEventListener('touchstart', handleTouchStart);\n    window.addEventListener('touchend', handleTouchEnd);\n\n    return () => {\n      window.removeEventListener('wheel', handleScroll);\n      window.removeEventListener('touchstart', handleTouchStart);\n      window.removeEventListener('touchend', handleTouchEnd);\n    };\n  }, [currentVideoIndex, videos.length]);\n\n  // Function to reload videos\n  const reloadVideos = () => {\n    setLoading(true);\n    // Trigger the useEffect to reload videos\n    window.location.reload();\n  };\n\n  // Empty state placeholder when no videos are available\n  const EmptyVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white',\n      padding: 3,\n      textAlign: 'center'\n    }}>\n      <Box sx={{\n        width: 80,\n        height: 80,\n        borderRadius: '50%',\n        bgcolor: 'rgba(255,255,255,0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        mb: 3\n      }}>\n        <PlayArrowIcon sx={{ fontSize: 40, opacity: 0.7 }} />\n      </Box>\n\n      <Typography variant=\"h6\" sx={{ mb: 1 }}>\n        Belum Ada Video\n      </Typography>\n\n      <Typography variant=\"body2\" sx={{ opacity: 0.7, maxWidth: 300, mb: 4 }}>\n        Admin belum menambahkan video. Video akan muncul di sini setelah admin menambahkannya.\n      </Typography>\n\n      {/* Refresh Button */}\n      <IconButton\n        onClick={reloadVideos}\n        sx={{\n          color: 'white',\n          bgcolor: 'rgba(255, 255, 255, 0.1)',\n          '&:hover': { bgcolor: 'rgba(255, 255, 255, 0.2)' },\n          mb: 3\n        }}\n      >\n        <i className=\"fas fa-refresh\" style={{ fontSize: 20 }} />\n      </IconButton>\n\n      <Typography variant=\"caption\" sx={{ opacity: 0.5 }}>\n        Ketuk untuk memuat ulang\n      </Typography>\n\n      {/* Placeholder UI Elements */}\n      <Box sx={{\n        display: 'flex',\n        gap: 4,\n        mt: 2\n      }}>\n        {/* Like Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <FavoriteBorderIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Comment Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ChatBubbleOutlineIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n\n        {/* Share Button */}\n        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>\n          <IconButton\n            disabled\n            sx={{\n              color: 'white',\n              bgcolor: 'rgba(255, 255, 255, 0.1)',\n              '&.Mui-disabled': { opacity: 0.5 }\n            }}\n          >\n            <ShareIcon />\n          </IconButton>\n          <Typography sx={{ color: 'white', fontSize: 12, mt: 0.5, opacity: 0.7 }}>\n            0\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n\n  // Loading state\n  const LoadingVideoState = () => (\n    <Box sx={{\n      height: '100vh',\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      bgcolor: '#000',\n      color: 'white'\n    }}>\n      <Box sx={{\n        width: 60,\n        height: 60,\n        borderRadius: '50%',\n        border: '3px solid rgba(255,255,255,0.3)',\n        borderTop: '3px solid white',\n        animation: 'spin 1s linear infinite',\n        mb: 2,\n        '@keyframes spin': {\n          '0%': { transform: 'rotate(0deg)' },\n          '100%': { transform: 'rotate(360deg)' }\n        }\n      }} />\n\n      <Typography variant=\"body1\">\n        Memuat Video...\n      </Typography>\n    </Box>\n  );\n\n  return (\n    <Box sx={{\n      height: '100vh',\n      overflow: 'hidden',\n      position: 'relative',\n      bgcolor: '#000',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center'\n    }}>\n      {loading ? (\n        <LoadingVideoState />\n      ) : videos.length > 0 ? (\n        <>\n          {videos.map((video, index) => (\n            <Box\n              key={video.id}\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                transform: `translateY(${(index - currentVideoIndex) * 100}%)`,\n                transition: 'transform 0.3s ease-in-out'\n              }}\n            >\n              <VideoItem\n                video={video}\n                isActive={index === currentVideoIndex}\n              />\n            </Box>\n          ))}\n\n          {/* Video Counter */}\n          <Box sx={{\n            position: 'absolute',\n            top: 20,\n            right: 20,\n            bgcolor: 'rgba(0, 0, 0, 0.5)',\n            borderRadius: 2,\n            px: 2,\n            py: 1,\n            zIndex: 4\n          }}>\n            <Typography sx={{ color: 'white', fontSize: 12 }}>\n              {currentVideoIndex + 1} / {videos.length}\n            </Typography>\n          </Box>\n        </>\n      ) : (\n        <EmptyVideoState />\n      )}\n    </Box>\n  );\n}\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,IAAI,EACJC,QAAQ,EACRC,aAAa,QACR,eAAe;AACtB,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,SAAS,MAAM,2BAA2B;AACjD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,OAAOC,SAAS,MAAM,2BAA2B;;AAEjD;;AAEA;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,SAASC,SAASA,CAAC;EAAEC,KAAK;EAAEC,QAAQ;EAAEC;AAAa,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6B,OAAO,EAAEC,UAAU,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAACuB,KAAK,CAACU,KAAK,CAACF,KAAK,CAAC;EACrD,MAAMG,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMkC,KAAK,GAAG3B,QAAQ,CAAC,CAAC;EACxB,MAAM4B,QAAQ,GAAG3B,aAAa,CAAC0B,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;;EAE5D;EACApC,SAAS,CAAC,MAAM;IACd,MAAMqC,eAAe,GAAG,MAAAA,CAAA,KAAY;MAClC,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,4FAA4F,CAAC;QAC1H,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5Cd,UAAU,CAACc,IAAI,CAACA,IAAI,CAACK,QAAQ,CAACxB,KAAK,CAACyB,EAAE,CAAC,CAAC;QAC1C;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAEDV,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAAChB,KAAK,CAACyB,EAAE,CAAC,CAAC;;EAEd;EACA,MAAMG,WAAW,GAAGf,QAAQ,GAAG,OAAO,GAAG,MAAM;EAC/C,MAAMgB,UAAU,GAAGhB,QAAQ,GAAG,OAAO,GAAG,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMiB,kBAAkB,GAAGA,CAACC,OAAO,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxD,MAAMC,MAAM,GAAG,IAAIC,eAAe,CAAC;MACjCF,QAAQ,EAAEA,QAAQ,GAAG,GAAG,GAAG,GAAG;MAC9BG,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAE,GAAG;MACbC,IAAI,EAAE,GAAG;MACTC,QAAQ,EAAEP,OAAO;MACjBQ,GAAG,EAAE,GAAG;MACRC,QAAQ,EAAE,GAAG;MACbC,cAAc,EAAE,GAAG;MACnBC,cAAc,EAAE,GAAG;MACnBC,EAAE,EAAE,GAAG;MACPC,SAAS,EAAE;IACb,CAAC,CAAC;IACF,OAAO,iCAAiCb,OAAO,IAAIE,MAAM,CAACY,QAAQ,CAAC,CAAC,EAAE;EACxE,CAAC;EAED,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC;IACAnB,OAAO,CAACoB,GAAG,CAAC,gBAAgB,EAAE/C,KAAK,CAACgD,KAAK,CAAC;IAE1C,IAAI;MACF;MACA,IAAI;QACF,MAAM9B,KAAK,CAAC,oCAAoClB,KAAK,CAACyB,EAAE,OAAO,EAAE;UAC/DwB,MAAM,EAAE;QACV,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOC,YAAY,EAAE;QACrB;QACA,MAAMhC,KAAK,CAAC,gGAAgGlB,KAAK,CAACyB,EAAE,EAAE,EAAE;UACtHwB,MAAM,EAAE;QACV,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC;EAED,MAAMyB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,MAAMC,QAAQ,GAAGhD,OAAO;;IAExB;IACAC,UAAU,CAAC,CAACD,OAAO,CAAC;IACpBK,QAAQ,CAAC4C,IAAI,IAAIjD,OAAO,GAAGiD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAE/C,IAAI;MACF1B,OAAO,CAACoB,GAAG,CAAC,0BAA0B,EAAE/C,KAAK,CAACyB,EAAE,CAAC;;MAEjD;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,uFAAuF,EAAE;QACpH+B,MAAM,EAAE,MAAM;QACdK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAMvD,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACoB,GAAG,CAAC,uBAAuB,EAAE5B,IAAI,CAAC;MAE1C,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACAhB,UAAU,CAACc,IAAI,CAACqC,KAAK,IAAI,CAACJ,QAAQ,CAAC;QACnC3C,QAAQ,CAACU,IAAI,CAACX,KAAK,KAAK4C,QAAQ,GAAG5C,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC,CAAC;QAC1DmB,OAAO,CAACoB,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC,MAAM;QACLpB,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEP,IAAI,CAACsC,OAAO,CAAC;QACjE;QACApD,UAAU,CAAC+C,QAAQ,CAAC;QACpB3C,QAAQ,CAAC4C,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;MAClD;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACArB,UAAU,CAAC+C,QAAQ,CAAC;MACpB3C,QAAQ,CAAC4C,IAAI,IAAID,QAAQ,GAAGC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;IAClD;EACF,CAAC;EAED,MAAMK,UAAU,GAAGA,CAAA,KAAM;IACvBnD,UAAU,CAAC,CAACD,OAAO,CAAC;EACtB,CAAC;EAED,MAAMqD,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC9BhC,OAAO,CAACoB,GAAG,CAAC,gBAAgB,EAAE/C,KAAK,CAACyB,EAAE,CAAC;IAEvC,IAAI;MACF;MACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,2FAA2F,EAAE;QACxH+B,MAAM,EAAE,MAAM;QACdK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE,MAAMvD,KAAK,CAACyB,EAAE;MACtB,CAAC,CAAC;MAEF,MAAMN,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCO,OAAO,CAACoB,GAAG,CAAC,iBAAiB,EAAE5B,IAAI,CAAC;MAEpC,IAAIA,IAAI,CAACE,OAAO,EAAE;QAChB;QACArB,KAAK,CAACU,KAAK,CAACkD,MAAM,GAAGzC,IAAI,CAACyC,MAAM,IAAI5D,KAAK,CAACU,KAAK,CAACkD,MAAM,GAAG,CAAC;QAC1DjC,OAAO,CAACoB,GAAG,CAAC,oCAAoC,CAAC;;QAEjD;QACA,IAAIc,SAAS,CAACC,KAAK,EAAE;UACnBD,SAAS,CAACC,KAAK,CAAC;YACdd,KAAK,EAAEhD,KAAK,CAACgD,KAAK;YAClBe,IAAI,EAAE/D,KAAK,CAACgE,WAAW;YACvBC,GAAG,EAAEjE,KAAK,CAACkE;UACb,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAL,SAAS,CAACM,SAAS,CAACC,SAAS,CAACpE,KAAK,CAACkE,QAAQ,CAAC;UAC7CG,KAAK,CAAC,wCAAwC,CAAC;QACjD;MACF,CAAC,MAAM;QACL1C,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEP,IAAI,CAACsC,OAAO,CAAC;MAChE;IACF,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAM4C,aAAa,GAAGA,CAAA,KAAM;IAC1B3C,OAAO,CAACoB,GAAG,CAAC,0BAA0B,EAAE/C,KAAK,CAACyB,EAAE,CAAC;EACnD,CAAC;EAED,MAAM8C,UAAU,GAAIC,UAAU,IAAK;IACjC,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,MAAMG,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAME,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAACH,GAAG,GAAGF,IAAI,CAAC;IACrC,MAAMM,QAAQ,GAAGF,IAAI,CAACG,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIG,QAAQ,KAAK,CAAC,EAAE,OAAO,aAAa;IACxC,IAAIA,QAAQ,GAAG,CAAC,EAAE,OAAO,GAAGA,QAAQ,YAAY;IAChD,IAAIA,QAAQ,GAAG,EAAE,EAAE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,cAAc;IAClE,OAAO,GAAGF,IAAI,CAACG,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,aAAa;EACjD,CAAC;EAED,oBACEnF,OAAA,CAAChB,GAAG;IAACqG,EAAE,EAAE;MACPC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAEvD,WAAW;MACnBwD,KAAK,EAAE,MAAM;MACbC,QAAQ,EAAExD,UAAU;MACpByD,EAAE,EAAE,MAAM;MACVC,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,QAAQ,EAAE,QAAQ;MAClBC,WAAW,EAAE,MAAM;MAAE;MACrBC,YAAY,EAAEhF,QAAQ,GAAG,CAAC,GAAG;IAC/B,CAAE;IAAAiF,QAAA,gBAEAlG,OAAA;MACEmG,GAAG,EAAEpF,SAAU;MACfqF,GAAG,EAAElE,kBAAkB,CAAC9B,KAAK,CAACiG,SAAS,EAAEhG,QAAQ,CAAE;MACnDiG,KAAK,EAAE;QACLd,KAAK,EAAE,MAAM;QACbD,MAAM,EAAE,MAAM;QACdgB,MAAM,EAAE,MAAM;QACdC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,OAAO,CAAC;MACrB,CAAE;MACFC,OAAO,EAAExD,gBAAiB;MAC1ByD,KAAK,EAAC,0FAA0F;MAChGC,eAAe;MACfxD,KAAK,EAAEhD,KAAK,CAACgD;IAAM;MAAAyD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAGFhH,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB2B,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTC,MAAM,EAAE,CAAC;QACTb,MAAM,EAAE;MACV,CAAE;MACFE,OAAO,EAAExD;IAAiB;MAAA2D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzB,CAAC,eAGFhH,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB6B,KAAK,EAAE,EAAE;QACTC,MAAM,EAAE,GAAG;QACXxB,OAAO,EAAE,MAAM;QACf0B,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE,CAAC;QACNF,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAlG,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACb,MAAM;UACLiH,GAAG,EAAEhG,KAAK,CAACoH,MAAM,CAACC,MAAO;UACzBpC,EAAE,EAAE;YACFG,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVgB,MAAM,EAAE,iBAAiB;YACzBC,MAAM,EAAE;UACV;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACD5G,KAAK,CAACoH,MAAM,CAACE,QAAQ,iBACpB1H,OAAA,CAAChB,GAAG;UAACqG,EAAE,EAAE;YACPM,OAAO,EAAE,SAAS;YAClBM,YAAY,EAAE,KAAK;YACnBT,KAAK,EAAE,EAAE;YACTD,MAAM,EAAE,EAAE;YACVK,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxB6B,EAAE,EAAE,CAAC,CAAC;YACNpB,MAAM,EAAE;UACV,CAAE;UAAAL,QAAA,eACAlG,OAAA,CAACf,UAAU;YAACoG,EAAE,EAAE;cAAEuC,KAAK,EAAE,OAAO;cAAEC,QAAQ,EAAE,EAAE;cAAEC,UAAU,EAAE;YAAO,CAAE;YAAA5B,QAAA,EAAC;UAAC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjF,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACTwH,OAAO,EAAEnD,UAAW;UACpB8B,EAAE,EAAE;YACFuC,KAAK,EAAEpH,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCmF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAED1F,OAAO,gBAAGR,OAAA,CAACT,YAAY;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhH,OAAA,CAACR,kBAAkB;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvDtF,KAAK,GAAG,IAAI,GAAG,GAAG,CAACA,KAAK,GAAG,IAAI,EAAEmH,OAAO,CAAC,CAAC,CAAC,GAAG,GAAGnH;QAAK;UAAAiG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACTwH,OAAO,EAAEhC,aAAc;UACvBW,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFlG,OAAA,CAACP,qBAAqB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD9F,KAAK,CAACU,KAAK,CAACkH;QAAQ;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACTwH,OAAO,EAAE5C,UAAW;UACpBuB,EAAE,EAAE;YACFuC,KAAK,EAAElH,OAAO,GAAG,SAAS,GAAG,OAAO;YACpCiF,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,EAEDxF,OAAO,gBAAGV,OAAA,CAACL,YAAY;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAGhH,OAAA,CAACN,kBAAkB;YAAAmH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EAAC;QAE3D;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACTwH,OAAO,EAAE3C,WAAY;UACrBsB,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,oBAAoB;YAC7B,SAAS,EAAE;cAAEA,OAAO,EAAE;YAAqB;UAC7C,CAAE;UAAAO,QAAA,eAEFlG,OAAA,CAACJ,SAAS;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE;UAAI,CAAE;UAAAzB,QAAA,EACvD9F,KAAK,CAACU,KAAK,CAACkD;QAAM;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhH,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPC,QAAQ,EAAE,UAAU;QACpB8B,MAAM,EAAE,EAAE;QACVF,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,EAAE;QACTE,MAAM,EAAE;MACV,CAAE;MAAAnB,QAAA,gBAEAlG,OAAA,CAACf,UAAU;QAACoG,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE;QACN,CAAE;QAAA/B,QAAA,GACCvB,UAAU,CAACvE,KAAK,CAAC8H,UAAU,CAAC,EAAC,UAAG,EAAC9H,KAAK,CAAC+H,QAAQ;MAAA;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtC,CAAC,eAGbhH,OAAA,CAACf,UAAU;QAACoG,EAAE,EAAE;UACduC,KAAK,EAAE,OAAO;UACdE,UAAU,EAAE,MAAM;UAClBD,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE;QACd,CAAE;QAAAlC,QAAA,EACC9F,KAAK,CAACgD;MAAK;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGbhH,OAAA,CAACf,UAAU;QAACoG,EAAE,EAAE;UACduC,KAAK,EAAE,0BAA0B;UACjCC,QAAQ,EAAE,EAAE;UACZI,EAAE,EAAE,CAAC;UACLG,UAAU,EAAE,GAAG;UACfxC,OAAO,EAAE,aAAa;UACtByC,eAAe,EAAE,CAAC;UAClBC,eAAe,EAAE,UAAU;UAC3BvC,QAAQ,EAAE;QACZ,CAAE;QAAAG,QAAA,EACC9F,KAAK,CAACgE;MAAW;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC,eAGbhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE2B,GAAG,EAAE,CAAC;UAAEgB,QAAQ,EAAE;QAAO,CAAE;QAAArC,QAAA,EACpD9F,KAAK,CAACoI,IAAI,CAACC,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBACzB3I,OAAA,CAACZ,IAAI;UAEHwJ,KAAK,EAAE,IAAIF,GAAG,EAAG;UACjBG,IAAI,EAAC,OAAO;UACZxD,EAAE,EAAE;YACFM,OAAO,EAAE,0BAA0B;YACnCiC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,EAAE;YACZtC,MAAM,EAAE,EAAE;YACV,SAAS,EAAE;cAAEI,OAAO,EAAE;YAA2B;UACnD;QAAE,GATGgD,KAAK;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAUX,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;;AAEA;AAAAzG,EAAA,CAnYSJ,SAAS;EAAA,QAKFd,QAAQ,EACLC,aAAa;AAAA;AAAAwJ,EAAA,GANvB3I,SAAS;AAoYlB,eAAe,SAAS4I,SAASA,CAAA,EAAG;EAAAC,GAAA;EAClC,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGrK,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACsK,MAAM,EAAEC,SAAS,CAAC,GAAGvK,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACwK,OAAO,EAAEC,UAAU,CAAC,GAAGzK,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAE,SAAS,CAAC,MAAM;IACd,MAAMwK,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7BD,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACFvH,OAAO,CAACoB,GAAG,CAAC,iCAAiC,CAAC;;QAE9C;QACA,MAAM9B,QAAQ,GAAG,MAAMC,KAAK,CAAC,gFAAgF,CAAC;QAE9G,IAAI,CAACD,QAAQ,CAACmI,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBpI,QAAQ,CAACqI,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMnI,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClCO,OAAO,CAACoB,GAAG,CAAC,eAAe,EAAE5B,IAAI,CAAC;QAElC,IAAIA,IAAI,CAACE,OAAO,IAAIC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAACA,IAAI,CAAC,EAAE;UAC5C;UACA,MAAMoI,QAAQ,GAAGpI,IAAI,CAACA,IAAI,CACvBqI,MAAM,CAACxJ,KAAK,IAAIA,KAAK,CAACsJ,MAAM,KAAK,WAAW,CAAC,CAAC;UAAA,CAC9CjB,GAAG,CAACrI,KAAK,IAAI;YAAA,IAAAyJ,cAAA,EAAAC,eAAA;YACZ,MAAMzD,SAAS,GAAGjG,KAAK,CAAC2J,UAAU,IAAIC,gBAAgB,CAAC5J,KAAK,CAAC6J,WAAW,CAAC;YACzE,OAAO;cACLpI,EAAE,EAAEzB,KAAK,CAACyB,EAAE;cACZyC,QAAQ,EAAElE,KAAK,CAAC6J,WAAW;cAC3B5D,SAAS,EAAEA,SAAS;cACpBjD,KAAK,EAAEhD,KAAK,CAACgD,KAAK,IAAI,gBAAgB;cACtCgB,WAAW,EAAEhE,KAAK,CAACgE,WAAW,IAAI,EAAAyF,cAAA,GAAAzJ,KAAK,CAAC8J,OAAO,cAAAL,cAAA,uBAAbA,cAAA,CAAeM,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAG,KAAK,IAAI,0BAA0B;cACxG3B,IAAI,EAAEpI,KAAK,CAACoI,IAAI,GAAGpI,KAAK,CAACoI,IAAI,CAAC4B,KAAK,CAAC,GAAG,CAAC,CAAC3B,GAAG,CAACC,GAAG,IAAIA,GAAG,CAAC2B,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,EAAAP,eAAA,GAAA1J,KAAK,CAACkK,QAAQ,cAAAR,eAAA,uBAAdA,eAAA,CAAgBS,WAAW,CAAC,CAAC,KAAI,OAAO,CAAC;cAC5G/C,MAAM,EAAE;gBACNgD,IAAI,EAAEpK,KAAK,CAACqK,UAAU,IAAI,eAAe;gBACzChD,MAAM,EAAE,gCAAgC,IAAIrH,KAAK,CAACyB,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;gBAC9D6F,QAAQ,EAAE;cACZ,CAAC;cACD5G,KAAK,EAAE;gBACLF,KAAK,EAAE8J,QAAQ,CAACtK,KAAK,CAACQ,KAAK,CAAC,IAAI,CAAC;gBACjCoH,QAAQ,EAAE0C,QAAQ,CAACtK,KAAK,CAACuK,cAAc,CAAC,IAAI,CAAC;gBAC7C3G,MAAM,EAAE0G,QAAQ,CAACtK,KAAK,CAAC4D,MAAM,CAAC,IAAI,CAAC;gBACnC4G,KAAK,EAAEF,QAAQ,CAACtK,KAAK,CAACwK,KAAK,CAAC,IAAI;cAClC,CAAC;cACD1C,UAAU,EAAE9H,KAAK,CAACyK,UAAU,IAAI,IAAI/F,IAAI,CAAC,CAAC,CAACgG,WAAW,CAAC,CAAC;cACxD3C,QAAQ,EAAE/H,KAAK,CAAC+H,QAAQ,IAAI,OAAO;cACnCmC,QAAQ,EAAElK,KAAK,CAACkK,QAAQ,IAAI,OAAO;cACnCS,SAAS,EAAE,8BAA8B1E,SAAS;YACpD,CAAC;UACH,CAAC,CAAC;UAEJtE,OAAO,CAACoB,GAAG,CAAC,mBAAmB,EAAEwG,QAAQ,CAAC;;UAE1C;UACA,IAAIA,QAAQ,CAACqB,MAAM,GAAG,CAAC,EAAE;YACvB5B,SAAS,CAACO,QAAQ,CAAC;YACnB5H,OAAO,CAACoB,GAAG,CAAC,gCAAgC,EAAEwG,QAAQ,CAACqB,MAAM,CAAC;UAChE,CAAC,MAAM;YACLjJ,OAAO,CAACoB,GAAG,CAAC,mDAAmD,CAAC;YAChEiG,SAAS,CAAC,EAAE,CAAC;UACf;QACF,CAAC,MAAM;UACLrH,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEP,IAAI,CAAC;UACnD6H,SAAS,CAAC,EAAE,CAAC;QACf;MACF,CAAC,CAAC,OAAOtH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD;QACAsH,SAAS,CAAC,EAAE,CAAC;MACf,CAAC,SAAS;QACRE,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMS,gBAAgB,GAAI3F,GAAG,IAAK;IAChC,IAAI,CAACA,GAAG,EAAE,OAAO,IAAI;IACrB,MAAM4G,MAAM,GAAG,8DAA8D;IAC7E,MAAMC,KAAK,GAAG7G,GAAG,CAAC6G,KAAK,CAACD,MAAM,CAAC;IAC/B,OAAQC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACF,MAAM,KAAK,EAAE,GAAIE,KAAK,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC,CAAC;EACvE,CAAC;;EAED;EACAnM,SAAS,CAAC,MAAM;IACd,IAAIoK,MAAM,CAAC6B,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;;IAEjC,IAAIG,MAAM,GAAG,CAAC;IACd,IAAIC,WAAW,GAAG,KAAK;IAEvB,MAAMC,YAAY,GAAIC,CAAC,IAAK;MAC1B,IAAIF,WAAW,EAAE;MACjBA,WAAW,GAAG,IAAI;MAElB,IAAIE,CAAC,CAACC,MAAM,GAAG,CAAC,IAAItC,iBAAiB,GAAGE,MAAM,CAAC6B,MAAM,GAAG,CAAC,EAAE;QACzD9B,oBAAoB,CAACzF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM,IAAI6H,CAAC,CAACC,MAAM,GAAG,CAAC,IAAItC,iBAAiB,GAAG,CAAC,EAAE;QAChDC,oBAAoB,CAACzF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;MACxC;MAEA+H,UAAU,CAAC,MAAM;QAAEJ,WAAW,GAAG,KAAK;MAAE,CAAC,EAAE,GAAG,CAAC;IACjD,CAAC;IAED,MAAMK,gBAAgB,GAAIH,CAAC,IAAK;MAC9BH,MAAM,GAAGG,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IAC/B,CAAC;IAED,MAAMC,cAAc,GAAIN,CAAC,IAAK;MAC5B,IAAIF,WAAW,EAAE;MACjB,MAAMS,IAAI,GAAGP,CAAC,CAACQ,cAAc,CAAC,CAAC,CAAC,CAACH,OAAO;MACxC,MAAMI,KAAK,GAAGZ,MAAM,GAAGU,IAAI;MAE3B,IAAI5G,IAAI,CAACC,GAAG,CAAC6G,KAAK,CAAC,GAAG,EAAE,EAAE;QAAE;QAC1BX,WAAW,GAAG,IAAI;QAElB,IAAIW,KAAK,GAAG,CAAC,IAAI9C,iBAAiB,GAAGE,MAAM,CAAC6B,MAAM,GAAG,CAAC,EAAE;UACtD;UACA9B,oBAAoB,CAACzF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC,CAAC,MAAM,IAAIsI,KAAK,GAAG,CAAC,IAAI9C,iBAAiB,GAAG,CAAC,EAAE;UAC7C;UACAC,oBAAoB,CAACzF,IAAI,IAAIA,IAAI,GAAG,CAAC,CAAC;QACxC;QAEA+H,UAAU,CAAC,MAAM;UAAEJ,WAAW,GAAG,KAAK;QAAE,CAAC,EAAE,GAAG,CAAC;MACjD;IACF,CAAC;IAEDY,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEZ,YAAY,CAAC;IAC9CW,MAAM,CAACC,gBAAgB,CAAC,YAAY,EAAER,gBAAgB,CAAC;IACvDO,MAAM,CAACC,gBAAgB,CAAC,UAAU,EAAEL,cAAc,CAAC;IAEnD,OAAO,MAAM;MACXI,MAAM,CAACE,mBAAmB,CAAC,OAAO,EAAEb,YAAY,CAAC;MACjDW,MAAM,CAACE,mBAAmB,CAAC,YAAY,EAAET,gBAAgB,CAAC;MAC1DO,MAAM,CAACE,mBAAmB,CAAC,UAAU,EAAEN,cAAc,CAAC;IACxD,CAAC;EACH,CAAC,EAAE,CAAC3C,iBAAiB,EAAEE,MAAM,CAAC6B,MAAM,CAAC,CAAC;;EAEtC;EACA,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzB7C,UAAU,CAAC,IAAI,CAAC;IAChB;IACA0C,MAAM,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,kBACtBtM,OAAA,CAAChB,GAAG;IAACqG,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE,OAAO;MACd2E,OAAO,EAAE,CAAC;MACVC,SAAS,EAAE;IACb,CAAE;IAAAtG,QAAA,gBACAlG,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBN,OAAO,EAAE,uBAAuB;QAChCC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBmC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eACAlG,OAAA,CAACH,aAAa;QAACwF,EAAE,EAAE;UAAEwC,QAAQ,EAAE,EAAE;UAAE4E,OAAO,EAAE;QAAI;MAAE;QAAA5F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAENhH,OAAA,CAACf,UAAU;MAACyN,OAAO,EAAC,IAAI;MAACrH,EAAE,EAAE;QAAE4C,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExC;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbhH,OAAA,CAACf,UAAU;MAACyN,OAAO,EAAC,OAAO;MAACrH,EAAE,EAAE;QAAEoH,OAAO,EAAE,GAAG;QAAEhH,QAAQ,EAAE,GAAG;QAAEwC,EAAE,EAAE;MAAE,CAAE;MAAA/B,QAAA,EAAC;IAExE;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhH,OAAA,CAACd,UAAU;MACTwH,OAAO,EAAEyF,YAAa;MACtB9G,EAAE,EAAE;QACFuC,KAAK,EAAE,OAAO;QACdjC,OAAO,EAAE,0BAA0B;QACnC,SAAS,EAAE;UAAEA,OAAO,EAAE;QAA2B,CAAC;QAClDsC,EAAE,EAAE;MACN,CAAE;MAAA/B,QAAA,eAEFlG,OAAA;QAAG2M,SAAS,EAAC,gBAAgB;QAACrG,KAAK,EAAE;UAAEuB,QAAQ,EAAE;QAAG;MAAE;QAAAhB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC,eAEbhH,OAAA,CAACf,UAAU;MAACyN,OAAO,EAAC,SAAS;MAACrH,EAAE,EAAE;QAAEoH,OAAO,EAAE;MAAI,CAAE;MAAAvG,QAAA,EAAC;IAEpD;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAGbhH,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPO,OAAO,EAAE,MAAM;QACf2B,GAAG,EAAE,CAAC;QACNI,EAAE,EAAE;MACN,CAAE;MAAAzB,QAAA,gBAEAlG,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACT0N,QAAQ;UACRvH,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAE8G,OAAO,EAAE;YAAI;UACnC,CAAE;UAAAvG,QAAA,eAEFlG,OAAA,CAACR,kBAAkB;YAAAqH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAE8E,OAAO,EAAE;UAAI,CAAE;UAAAvG,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACT0N,QAAQ;UACRvH,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAE8G,OAAO,EAAE;YAAI;UACnC,CAAE;UAAAvG,QAAA,eAEFlG,OAAA,CAACP,qBAAqB;YAAAoH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACf,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAE8E,OAAO,EAAE;UAAI,CAAE;UAAAvG,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAE0B,aAAa,EAAE,QAAQ;UAAEzB,UAAU,EAAE;QAAS,CAAE;QAAAK,QAAA,gBAC1ElG,OAAA,CAACd,UAAU;UACT0N,QAAQ;UACRvH,EAAE,EAAE;YACFuC,KAAK,EAAE,OAAO;YACdjC,OAAO,EAAE,0BAA0B;YACnC,gBAAgB,EAAE;cAAE8G,OAAO,EAAE;YAAI;UACnC,CAAE;UAAAvG,QAAA,eAEFlG,OAAA,CAACJ,SAAS;YAAAiH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACbhH,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE,EAAE;YAAEF,EAAE,EAAE,GAAG;YAAE8E,OAAO,EAAE;UAAI,CAAE;UAAAvG,QAAA,EAAC;QAEzE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CACN;;EAED;EACA,MAAM6F,iBAAiB,GAAGA,CAAA,kBACxB7M,OAAA,CAAChB,GAAG;IAACqG,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfK,OAAO,EAAE,MAAM;MACf0B,aAAa,EAAE,QAAQ;MACvBzB,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBH,OAAO,EAAE,MAAM;MACfiC,KAAK,EAAE;IACT,CAAE;IAAA1B,QAAA,gBACAlG,OAAA,CAAChB,GAAG;MAACqG,EAAE,EAAE;QACPG,KAAK,EAAE,EAAE;QACTD,MAAM,EAAE,EAAE;QACVU,YAAY,EAAE,KAAK;QACnBM,MAAM,EAAE,iCAAiC;QACzCuG,SAAS,EAAE,iBAAiB;QAC5BC,SAAS,EAAE,yBAAyB;QACpC9E,EAAE,EAAE,CAAC;QACL,iBAAiB,EAAE;UACjB,IAAI,EAAE;YAAE+E,SAAS,EAAE;UAAe,CAAC;UACnC,MAAM,EAAE;YAAEA,SAAS,EAAE;UAAiB;QACxC;MACF;IAAE;MAAAnG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAELhH,OAAA,CAACf,UAAU;MAACyN,OAAO,EAAC,OAAO;MAAAxG,QAAA,EAAC;IAE5B;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CACN;EAED,oBACEhH,OAAA,CAAChB,GAAG;IAACqG,EAAE,EAAE;MACPE,MAAM,EAAE,OAAO;MACfQ,QAAQ,EAAE,QAAQ;MAClBT,QAAQ,EAAE,UAAU;MACpBK,OAAO,EAAE,MAAM;MACfC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE;IAClB,CAAE;IAAAI,QAAA,EACCmD,OAAO,gBACNrJ,OAAA,CAAC6M,iBAAiB;MAAAhG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,GACnBmC,MAAM,CAAC6B,MAAM,GAAG,CAAC,gBACnBhL,OAAA,CAAAE,SAAA;MAAAgG,QAAA,GACGiD,MAAM,CAACV,GAAG,CAAC,CAACrI,KAAK,EAAEuI,KAAK,kBACvB3I,OAAA,CAAChB,GAAG;QAEFqG,EAAE,EAAE;UACFC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACP1B,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdK,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBkH,SAAS,EAAE,cAAc,CAACrE,KAAK,GAAGM,iBAAiB,IAAI,GAAG,IAAI;UAC9DgE,UAAU,EAAE;QACd,CAAE;QAAA/G,QAAA,eAEFlG,OAAA,CAACG,SAAS;UACRC,KAAK,EAAEA,KAAM;UACbC,QAAQ,EAAEsI,KAAK,KAAKM;QAAkB;UAAApC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC,GAjBG5G,KAAK,CAACyB,EAAE;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAkBV,CACN,CAAC,eAGFhH,OAAA,CAAChB,GAAG;QAACqG,EAAE,EAAE;UACPC,QAAQ,EAAE,UAAU;UACpB2B,GAAG,EAAE,EAAE;UACPE,KAAK,EAAE,EAAE;UACTxB,OAAO,EAAE,oBAAoB;UAC7BM,YAAY,EAAE,CAAC;UACfiH,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACL9F,MAAM,EAAE;QACV,CAAE;QAAAnB,QAAA,eACAlG,OAAA,CAACf,UAAU;UAACoG,EAAE,EAAE;YAAEuC,KAAK,EAAE,OAAO;YAAEC,QAAQ,EAAE;UAAG,CAAE;UAAA3B,QAAA,GAC9C+C,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACE,MAAM,CAAC6B,MAAM;QAAA;UAAAnE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA,eACN,CAAC,gBAEHhH,OAAA,CAACsM,eAAe;MAAAzF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACgC,GAAA,CA7VuBD,SAAS;AAAAqE,GAAA,GAATrE,SAAS;AAAA,IAAAD,EAAA,EAAAsE,GAAA;AAAAC,YAAA,CAAAvE,EAAA;AAAAuE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}