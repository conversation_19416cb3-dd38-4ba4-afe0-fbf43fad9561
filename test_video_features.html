<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Video Features</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Like Button States */
        .like-btn {
            transition: all 0.3s ease;
        }

        .like-btn.liked {
            background-color: rgba(239, 68, 68, 0.2) !important;
            border-color: rgba(239, 68, 68, 0.5) !important;
        }

        .like-btn.liked i {
            color: #ef4444 !important;
            animation: heartBeat 0.6s ease-in-out;
        }

        .like-animation {
            animation: likeAnimation 0.3s ease-in-out;
        }

        @keyframes heartBeat {
            0% { transform: scale(1); }
            25% { transform: scale(1.2); }
            50% { transform: scale(1); }
            75% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @keyframes likeAnimation {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Save Button States */
        .save-btn {
            transition: all 0.3s ease;
        }

        .save-btn.saved {
            background-color: rgba(251, 191, 36, 0.2) !important;
            border-color: rgba(251, 191, 36, 0.5) !important;
        }

        .save-btn.saved i {
            color: #fbbf24 !important;
        }

        .save-animation {
            animation: saveAnimation 0.3s ease-in-out;
        }

        @keyframes saveAnimation {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto p-8">
        <h1 class="text-3xl font-bold mb-8 text-center">Test Video Features</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Like Button Test -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Like Button Test</h2>
                <div class="flex flex-col items-center space-y-4">
                    <button
                        onclick="testLike()"
                        class="like-btn w-16 h-16 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-all duration-300 border border-gray-600"
                        id="likeBtn"
                    >
                        <i class="fas fa-heart text-white text-2xl"></i>
                    </button>
                    <span class="text-white text-sm font-semibold" id="likeCount">0</span>
                    <p class="text-gray-400 text-sm text-center">Click to test like functionality</p>
                </div>
            </div>

            <!-- Save Button Test -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h2 class="text-xl font-semibold mb-4">Save Button Test</h2>
                <div class="flex flex-col items-center space-y-4">
                    <button
                        onclick="testSave()"
                        class="save-btn w-16 h-16 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-all duration-300 border border-gray-600"
                        id="saveBtn"
                    >
                        <i class="fas fa-bookmark text-white text-2xl"></i>
                    </button>
                    <span class="text-white text-sm font-semibold">Simpan</span>
                    <p class="text-gray-400 text-sm text-center">Click to test save functionality</p>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">Test Results</h2>
            <div id="testResults" class="space-y-2 text-sm">
                <p class="text-gray-400">Click buttons above to test functionality...</p>
            </div>
        </div>
    </div>

    <script>
        let isLiked = false;
        let isSaved = false;
        let likeCount = 0;

        function testLike() {
            const likeBtn = document.getElementById('likeBtn');
            const likeCountEl = document.getElementById('likeCount');
            const likeIcon = likeBtn.querySelector('i');
            
            // Toggle like state
            isLiked = !isLiked;
            
            // Add animation
            likeBtn.classList.add('like-animation');
            setTimeout(() => likeBtn.classList.remove('like-animation'), 300);
            
            if (isLiked) {
                likeBtn.classList.add('liked');
                likeIcon.classList.remove('text-white');
                likeIcon.classList.add('text-red-500');
                likeCount++;
                addTestResult('✅ Like button activated - turned red', 'text-green-400');
            } else {
                likeBtn.classList.remove('liked');
                likeIcon.classList.remove('text-red-500');
                likeIcon.classList.add('text-white');
                likeCount--;
                addTestResult('❌ Like button deactivated - turned white', 'text-yellow-400');
            }
            
            likeCountEl.textContent = likeCount;
        }

        function testSave() {
            const saveBtn = document.getElementById('saveBtn');
            const saveIcon = saveBtn.querySelector('i');
            
            // Toggle save state
            isSaved = !isSaved;
            
            // Add animation
            saveBtn.classList.add('save-animation');
            setTimeout(() => saveBtn.classList.remove('save-animation'), 300);
            
            if (isSaved) {
                saveBtn.classList.add('saved');
                saveIcon.classList.remove('text-white');
                saveIcon.classList.add('text-yellow-400');
                addTestResult('💾 Save button activated - turned yellow', 'text-green-400');
            } else {
                saveBtn.classList.remove('saved');
                saveIcon.classList.remove('text-yellow-400');
                saveIcon.classList.add('text-white');
                addTestResult('🗑️ Save button deactivated - turned white', 'text-yellow-400');
            }
        }

        function addTestResult(message, colorClass) {
            const resultsDiv = document.getElementById('testResults');
            const p = document.createElement('p');
            p.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            p.className = colorClass;
            resultsDiv.appendChild(p);
            
            // Keep only last 10 results
            while (resultsDiv.children.length > 10) {
                resultsDiv.removeChild(resultsDiv.firstChild);
            }
        }

        // Initial test result
        addTestResult('Test page loaded successfully', 'text-blue-400');
    </script>
</body>
</html>
