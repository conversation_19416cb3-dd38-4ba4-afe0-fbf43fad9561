<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../config.php';

// Get database connection
$pdo = getConnection();

// Get client IP address
function getClientIP() {
    $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
}

$method = $_SERVER['REQUEST_METHOD'];
$input = json_decode(file_get_contents('php://input'), true);

try {
    switch ($method) {
        case 'GET':
            // Check if user has liked a video
            $videoId = $_GET['video_id'] ?? null;
            if (!$videoId) {
                throw new Exception('Video ID is required');
            }

            $ip = getClientIP();

            $stmt = $pdo->prepare("
                SELECT COUNT(*) as liked 
                FROM video_likes 
                WHERE video_id = ? AND ip_address = ?
            ");
            $stmt->execute([$videoId, $ip]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            // Get current like count
            $stmt = $pdo->prepare("SELECT likes FROM videos WHERE id = ?");
            $stmt->execute([$videoId]);
            $video = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'liked' => $result['liked'] > 0,
                'likes_count' => $video['likes'] ?? 0
            ]);
            break;

        case 'POST':
            // Toggle like for a video
            $videoId = $input['video_id'] ?? null;
            if (!$videoId) {
                throw new Exception('Video ID is required');
            }

            $ip = getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';

            // Check if user has already liked this video
            $stmt = $pdo->prepare("
                SELECT id FROM video_likes 
                WHERE video_id = ? AND ip_address = ?
            ");
            $stmt->execute([$videoId, $ip]);
            $existingLike = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($existingLike) {
                // Unlike - remove the like
                $stmt = $pdo->prepare("
                    DELETE FROM video_likes 
                    WHERE video_id = ? AND ip_address = ?
                ");
                $stmt->execute([$videoId, $ip]);

                // Decrease like count in videos table
                $stmt = $pdo->prepare("
                    UPDATE videos 
                    SET likes = GREATEST(0, likes - 1) 
                    WHERE id = ?
                ");
                $stmt->execute([$videoId]);

                $action = 'unliked';
            } else {
                // Like - add new like
                $stmt = $pdo->prepare("
                    INSERT INTO video_likes (video_id, ip_address, user_agent, liked_at)
                    VALUES (?, ?, ?, NOW())
                ");
                $stmt->execute([$videoId, $ip, $userAgent]);

                // Increase like count in videos table
                $stmt = $pdo->prepare("
                    UPDATE videos 
                    SET likes = likes + 1 
                    WHERE id = ?
                ");
                $stmt->execute([$videoId]);

                $action = 'liked';
            }

            // Get updated like count
            $stmt = $pdo->prepare("SELECT likes FROM videos WHERE id = ?");
            $stmt->execute([$videoId]);
            $video = $stmt->fetch(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'action' => $action,
                'liked' => $action === 'liked',
                'likes_count' => $video['likes'] ?? 0
            ]);
            break;

        default:
            throw new Exception('Method not allowed');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
