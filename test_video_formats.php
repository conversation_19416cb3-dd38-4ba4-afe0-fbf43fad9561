<?php
require_once 'frontend/src/pages/admin/config.php';

try {
    $pdo = getConnection();
    
    echo "Testing video format support...\n";
    
    // Sample video data for different formats
    $sampleVideos = [
        [
            'title' => 'Sample MP4 Video',
            'description' => 'Test video in MP4 format',
            'video_path' => '/uploads/sample_video.mp4',
            'file_size' => 15728640, // 15MB
            'file_format' => 'mp4',
            'video_type' => 'upload'
        ],
        [
            'title' => 'Sample AVI Video',
            'description' => 'Test video in AVI format',
            'video_path' => '/uploads/sample_video.avi',
            'file_size' => 25165824, // 24MB
            'file_format' => 'avi',
            'video_type' => 'upload'
        ],
        [
            'title' => 'Sample MOV Video',
            'description' => 'Test video in MOV format',
            'video_path' => '/uploads/sample_video.mov',
            'file_size' => 18874368, // 18MB
            'file_format' => 'mov',
            'video_type' => 'upload'
        ],
        [
            'title' => 'Sample WebM Video',
            'description' => 'Test video in WebM format',
            'video_path' => '/uploads/sample_video.webm',
            'file_size' => 12582912, // 12MB
            'file_format' => 'webm',
            'video_type' => 'upload'
        ],
        [
            'title' => 'Sample YouTube Video',
            'description' => 'Test YouTube video',
            'youtube_url' => 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
            'youtube_id' => 'dQw4w9WgXcQ',
            'video_type' => 'youtube'
        ]
    ];
    
    // Insert sample videos
    $stmt = $pdo->prepare("INSERT INTO videos (
        title, description, content, youtube_url, youtube_id,
        video_path, file_size, file_format, video_type,
        thumbnail, category, tags, duration, status,
        featured, created_at, updated_at
    ) VALUES (
        ?, ?, ?, ?, ?,
        ?, ?, ?, ?,
        ?, ?, ?, ?, ?,
        ?, NOW(), NOW()
    )");
    
    foreach ($sampleVideos as $video) {
        $result = $stmt->execute([
            $video['title'],
            $video['description'],
            'Sample video content for testing',
            $video['youtube_url'] ?? '',
            $video['youtube_id'] ?? '',
            $video['video_path'] ?? '',
            $video['file_size'] ?? 0,
            $video['file_format'] ?? '',
            $video['video_type'],
            '', // thumbnail
            'Technology', // category
            'sample,test,' . ($video['file_format'] ?? 'youtube'), // tags
            '02:30', // duration
            'published', // status
            0 // featured
        ]);
        
        if ($result) {
            $videoId = $pdo->lastInsertId();
            echo "✓ Created {$video['video_type']} video: {$video['title']} (ID: $videoId)\n";
        } else {
            echo "✗ Failed to create video: {$video['title']}\n";
        }
    }
    
    echo "\nTesting video source function...\n";
    
    // Test getVideoSource function
    function getVideoSource($video) {
        $video_type = $video['video_type'] ?? 'youtube';
        
        if ($video_type === 'upload' && !empty($video['video_path'])) {
            return [
                'type' => 'upload',
                'src' => $video['video_path'],
                'format' => $video['file_format'] ?? 'mp4',
                'size' => $video['file_size'] ?? 0
            ];
        } else {
            $youtubeId = $video['youtube_id'] ?? '';
            return [
                'type' => 'youtube',
                'src' => "https://www.youtube.com/embed/{$youtubeId}",
                'youtube_id' => $youtubeId
            ];
        }
    }
    
    function formatFileSize($bytes) {
        if ($bytes == 0) return '0 Bytes';
        $k = 1024;
        $sizes = array('Bytes', 'KB', 'MB', 'GB', 'TB');
        $i = floor(log($bytes) / log($k));
        return round($bytes / pow($k, $i), 2) . ' ' . $sizes[$i];
    }
    
    // Get recent videos and test
    $stmt = $pdo->query("SELECT * FROM videos ORDER BY created_at DESC LIMIT 10");
    $videos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nRecent videos and their sources:\n";
    foreach ($videos as $video) {
        $source = getVideoSource($video);
        $type = $source['type'];
        $format = $source['format'] ?? 'N/A';
        $size = isset($source['size']) ? formatFileSize($source['size']) : 'N/A';
        
        echo "- {$video['title']}\n";
        echo "  Type: $type, Format: $format, Size: $size\n";
        echo "  Source: {$source['src']}\n\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
