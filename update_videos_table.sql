-- Update videos table to support file uploads
-- Add columns for video file path, size, and format

ALTER TABLE `videos`
ADD COLUMN IF NOT EXISTS `video_path` VARCHAR(500) NULL AFTER `youtube_id`,
ADD COLUMN IF NOT EXISTS `file_size` BIGINT NULL COMMENT 'File size in bytes' AFTER `video_path`,
ADD COLUMN IF NOT EXISTS `file_format` VARCHAR(10) NULL COMMENT 'Video format (mp4, avi, mov, etc)' AFTER `file_size`,
ADD COLUMN IF NOT EXISTS `video_type` ENUM('youtube', 'upload') DEFAULT 'youtube' AFTER `file_format`;

-- Update existing records to set video_type
UPDATE `videos` SET `video_type` = 'youtube' WHERE `youtube_url` IS NOT NULL AND `youtube_url` != '';

-- Show table structure
DESCRIBE `videos`;
